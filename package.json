{"name": "test", "version": "1.0.0", "description": "Test", "main": "server.js", "scripts": {"start": "node server.js"}, "dependencies": {"axios": "^1.7.7", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.21.2", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "mysql2": "^3.9.1", "ngx-scanner-qrcode": "^1.7.6", "open": "^7.3.0", "papaparse": "^5.4.1"}}