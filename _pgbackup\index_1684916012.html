<!DOCTYPE html> 
<html ng-app="myApp"> 
    <head> 
        <title>CSV File</title>         
        <link rel="stylesheet" href="styles.css"> 
    </head>     
    <body>          
        <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>         
        <script src="app.js"></script>
        <div id="container" ng-controller="MainController">              
            <label class="file-label"> 
                Select from Device
                <input type="file" ng-model="file" ng-change="processFile()" accept=".csv" class="file-input"> 
            </label>
            <div id="dropbox" ng-class="{'dragover': isDragOver}" ng-drop="onFileDrop($event)"> 
                <div ng-if="!file"> <span>Drag and drop a CSV file here</span> 
                </div>                 
                <div ng-if="file">File selected: {{file.name}}</div>                 
            </div>             
            <textarea id="jsonData" ng-model="filteredEmailsText" readonly></textarea> 
        </div>         
    </body>     
</html>
