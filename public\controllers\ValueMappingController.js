angular.module('myApp')
  .controller('ValueMappingController', function($scope, valueMappingService, $mdDialog) {
    $scope.uniqueStarValues = valueMappingService.getMappedValues();
    $scope.valueMapping = {}; 

    $scope.saveMapping = function() {
      console.log("Mapped Values:", $scope.valueMapping);
      $mdDialog.hide($scope.valueMapping);
    };
    $scope.isAllFilled = function() {
        return $scope.uniqueStarValues.every(value => !!$scope.valueMapping[value]);
    };

    $scope.closeDialog = function() {
      $mdDialog.hide(); 
    };
  });
