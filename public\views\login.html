
    <!-- Login Box -->
    <div ng-controller="LoginController" class="loginContainer" ng-hide="isLoggedIn" >
        <div class="wrapper">
          <div class="login-box">
            <div class="login-header">
              <!-- <span>Login</span> -->
              <div class="login-header">
                <img src="assets/images/image_embedded.svg" alt="Privacy Icon" class="privacy-icon">
              </div>
            </div>
            <div class="input_box">
              <input type="text" id="user" class="input-field" ng-model="loginData.username" required>
              <label for="user" class="label">Email</label>
              <i class="bx bx-user icon"></i>
            </div>
            <div class="input_box">
              <input type="password" id="pass" class="input-field" ng-model="loginData.password" required ng-type="password">
              <label for="pass" class="label">Password</label>
              <i class="bx icon" ng-class="{'bx-hide': !isPasswordVisible, 'bx-show': isPasswordVisible}" ng-click="togglePassword()"></i>
            </div>
            <div class="input_box">
              <input
              type="submit"
              class="input-submit"
              value="Login"
              ng-click="login()"
              ng-disabled="!loginData.username || !loginData.password"
              ng-class="{'disabled-btn': !loginData.username || !loginData.password}">            
            </div>
            <img ng-if="showLoginLoader" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px;">
            <div class="error-message" ng-show="error">{{ errorMessage }}</div>
          </div>
        </div>
      </div>