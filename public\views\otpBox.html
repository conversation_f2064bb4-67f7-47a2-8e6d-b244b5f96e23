<md-dialog aria-label="Enter OTP">
    <md-dialog-content style="display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
        <h2 style="color: #fff !important;">Enter OTP</h2>
        <!-- <div style="display: flex; gap: 10px;">
            <input type="number" maxlength="1" ng-model="otp[0]" ng-keyup="moveToNext($event, 0)" style="width: 40px; text-align: center;" required>
            <input type="number" maxlength="1" ng-model="otp[1]" ng-keyup="moveToNext($event, 1)" style="width: 40px; text-align: center;" required>
            <input type="number" maxlength="1" ng-model="otp[2]" ng-keyup="moveToNext($event, 2)" style="width: 40px; text-align: center;" required>
            <input type="number" maxlength="1" ng-model="otp[3]" ng-keyup="moveToNext($event, 3)" style="width: 40px; text-align: center;" required>
        </div>         -->

        <div style="display: flex; gap: 10px;">
            <input type="text" 
       maxlength="4" 
       pattern="\d{4}" 
       ng-model="otpCode" 
       ng-keyup="formatOtp()" 
       style="width: 140px; text-align: center; letter-spacing: 8px; font-size: 18px;" 
       required>

          </div> 
        
        <div ng-show="errorMessage" class="error-message">
            <p>{{ errorMessage }}</p>
        </div>
    </md-dialog-content>
    
    <md-dialog-actions style="padding-left: 10px !important; padding-right: 10px !important;">
        <img ng-if="showOtpLoader" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px;">
        <md-button class="btn-body sec-button md-button ng-scope md-ink-ripple" ng-click="verifyOtp()">Verify OTP</md-button>
    </md-dialog-actions>
</md-dialog>
