md-tabs-wrapper{
  background-color: #fff !important;
  color: #ffffff !important;
}

.analytics-container {
  border: 2px dashed #FFF;
  background-color: rgba(252, 251, 251, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px !important;
  padding: 30px;
  padding-top: 40px;
  padding-bottom: 40px;
  width: 900px !important; 
  height: 80% !important;
  display: block; 
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
  font-family: 'Roboto', sans-serif;
}

@media only screen and (max-width: 768px) {
  .analytics-container {
    width: auto !important; 
    padding: 20px; 
    height: auto; 
    min-width: 380px;
  }
}

md-tabs.md-primary {
  background-color: #3f51b5;
  color: white;
  margin-bottom: 20px;
}

md-tab {
  text-transform: uppercase;
}

.md-tab-label {
  color: #ffffff !important;
}

.calendar-container {
  border-radius: 5px;
}

.search-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}
.search-box input{
  margin: 0px 10px 0px !important;
}
.search-box input:focus{
  color: #313A42 !important;
}
.dropdown-text {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #333;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  display: none;
}

.dropdown-list.show {
  display: block;
}

.dropdown-list-item {
  padding: 10px;
  cursor: pointer;
}

.dropdown-list-item:hover {
  background-color: #f0f0f0;
}

md-content, md-tabs.md-primary{
  border: 1px solid #ddd;
  border-radius: 5px;

}
.md-tile-content h3 {
  text-align: left !important;
  margin-bottom: 10px;
  font-size: 26px;
  font-weight: bold;
  color: #333 !important;
}

.md-tile-content h6 {
  font-size: 14px;
  color: #666;
}

.md-tile-left img {
  width: 25px;
  height: 40px;
  margin-right: 10px;
}

.md-divider {
  margin: 5px 0;
}

.md-button {
  color: #3f51b5;
}

.btn-body {
  font-size: 13px;
  color: #3f51b5;
}

.btn-body.md-button:hover {
  background-color: #e8eaf6;
}

.md-fab {
  background-color: #3f51b5;
  color: #ffffff;
}

.md-fab md-icon {
  fill: #ffffff;
}
md-input-container input {
  padding-left: 30px;
}

md-input-container md-icon {
  color: #555; 
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
}

.md-button {
  border-radius: 8px; 
  font-weight: bold;  
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}
