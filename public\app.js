angular
  .module("myApp", ["ngRoute", "ngMaterial", "ngAnimate"])

  .config([
    "$routeProvider",
    function ($routeProvider) {
      $routeProvider
        .when("/", {
          templateUrl: "./views/qr_scanner.html",
          controller: "MainController",
        })
        .when("/home", {
          templateUrl: "./views/home.html",
          controller: "MainController",
        })
        .when("/analytics", {
          templateUrl: "./views/analytics.html",
          controller: "AnalyticsController",
        })
        .when("/raffle", {
          templateUrl: "./views/raffleDraw.html",
          controller: "AnalyticsController",
        })
        .when("/login", {
          templateUrl: "./views/login.html",
          controller: "LoginController",
        })
        .when("/qr", {
          templateUrl: "./views/qr_scanner.html",
          controller: "MainController",
        })
        .when("/landing", {
          templateUrl: "./views/landingHomePage.html",
          controller: "MainController",
        })
        .otherwise({
          redirectTo: "/login",
        });
    },
  ])

  .controller("MainController", [
    "$scope",
    "$http",
    "$window",
    "$mdDialog",
    "valueMappingService",
    "alertService",
    function (
      $scope,
      $http,
      $window,
      $mdDialog,
      valueMappingService,
      alertService
    ) {
      $scope.isDragOver = false;
      $scope.file = null;
      $scope.showLoader = false;
      $scope.showLoaderID = false;
      $scope.showFinal = false;
      $scope.showdropbox = false;
      $scope.valuebox = false;
      $scope.showIndex = true;
      $scope.apiCallCount = 0;
      $scope.apiCallCount2 = 0;
      $scope.idInput = "";
      $scope.responses = 0;
      $scope.latitude = null;
      $scope.longitude = null;
      $scope.isProcessing = false;
      $scope.hasStarType = false;

      $scope.valueMapping = {};
      let matrixValueMap = [];
      let multiValueMap = [];
      let uniqueMultiValues = {};

      $scope.openValueMapping = function () {
        $mdDialog
          .show({
            controller: "ValueMappingController",
            templateUrl: "./views/valueMapping.html",
            parent: angular.element(document.body),
            clickOutsideToClose: false,
          })
          .then(
            function (valueMapping) {
              console.log("Returned Mapping:", valueMapping);
              $scope.valueMapping = valueMapping;
              $scope.showdropbox = false;

              console.log("Returned Mapping:123", $scope.valueMapping);
            },
            function () {
              console.log("Dialog closed without saving.");
            }
          );
      };

      $scope.clickDropbox = function () {
        document.getElementById("fileInput").click();
      };
      $scope.onFileSelect = function (file) {
        if (file) {
          $scope.file = file;
          $scope.processFile();
        } else {
          console.error("No file selected or file is null.");
        }
      };

      $scope.onFileDrop = function (event) {
        event.preventDefault();
        event.stopPropagation();

        $scope.isDragOver = false;
        $scope.file = event.dataTransfer.files[0];
        $scope.processFile();
      };

      $scope.processFile = function () {
        if ($scope.file) {
          var reader = new FileReader();

          reader.onload = function (e) {
            var contents = e.target.result;
            $scope.processValues(contents);
            localStorage.setItem("fileContents", contents);
            console.log("File contents saved to local storage.");
            console.log("Latitude2: " + $scope.latitude);
            console.log("Longitude2: " + $scope.longitude);
            $scope.$apply();
          };

          reader.readAsText($scope.file);
        }
      };
      $scope.surveyIdInput = async function () {
        const eventcode = "KCEV" + $scope.idInput;
        $scope.showLoaderID = true;

        try {
          const response = await $http({
            method: "POST",
            url: "https://crowdsnap.ai/api/polls/isvalidevent",
            data: { eventcode: eventcode },
          });

          if (response.data == true) {
            console.log("The event ID is valid.");
            $scope.showdropbox = true;
            $scope.showIndex = false;
          } else {
            console.log("The event ID is invalid.");
            alert("Invalid event ID. Please enter a valid ID.");
          }
        } catch (error) {
          console.error("Error occurred during event ID validation:", error);
          alert(
            "An error occurred while validating the event ID. Please try again."
          );
        } finally {
          $scope.showLoaderID = false;
          $scope.$apply();
        }
      };

      $scope.getLocation = function () {
        if ($window.navigator.geolocation) {
          $window.navigator.geolocation.getCurrentPosition(
            function (position) {
              $scope.latitude = position.coords.latitude;
              $scope.longitude = position.coords.longitude;
              console.log("Latitude: " + $scope.latitude);
              console.log("Longitude: " + $scope.longitude);
              $scope.$apply();
            },
            function (error) {
              console.error("Error getting location:", error);
            }
          );
        } else {
          console.error("Geolocation is not supported by this browser.");
        }
      };

      $scope.processValues = async function (csv) {
        Papa.parse(csv, {
          complete: async function (results) {
            var lines = results.data;
            var headers = lines[0].map((header) =>
              header.trim().replace(/'/g, "")
            );
            var pollIds = lines[1].map((id) => id.trim());
            var types = lines[2].map((type) => type.trim());

            for (let i = 3; i < lines.length; i++) {
              let row = lines[i];

              for (let j = 0; j < row.length; j++) {
                let value = row[j].trim();
                let pollId = pollIds[j];
                let type = types[j];
                let header = headers[j];

                if (type === "matrix") {
                  let matrixEntry = matrixValueMap.find(
                    (entry) => entry.pollId === pollId
                  );

                  if (!matrixEntry) {
                    matrixEntry = { pollId, rows: [], columns: new Set() };
                    matrixValueMap.push(matrixEntry);
                  }

                  if (!matrixEntry.rows.includes(header)) {
                    matrixEntry.rows.push(header);
                  }

                  value
                    .split(",")
                    .map((val) => val.trim())
                    .filter((val) => val !== "")
                    .forEach((val) => matrixEntry.columns.add(val));
                }
                if (type === "star") {
                  $scope.hasStarType = true;
                  let starValues = value.split(",");
                  valueMappingService.captureStarValues(pollId, starValues);
                }
                if (type === "multi") {
                  if (!multiValueMap[pollId]) {
                    multiValueMap[pollId] = [];
                  }

                  value
                    .split(",")
                    .map((val) => val.trim())
                    .filter((val) => val !== "")
                    .forEach((val) => {
                      multiValueMap[pollId].push(val);
                    });
                }
              }
            }

            matrixValueMap.forEach((entry) => {
              entry.columns = Array.from(entry.columns);
            });

            console.log("Matrix Value Map:", matrixValueMap);
            $scope.matrixValueMap = matrixValueMap;

            Object.keys(multiValueMap).forEach((pollId) => {
              const valueCounts = {};
              multiValueMap[pollId].forEach((val) => {
                valueCounts[val] = (valueCounts[val] || 0) + 1;
              });

              uniqueMultiValues[pollId] = [];

              multiValueMap[pollId] = multiValueMap[pollId].map((val) => {
                if (valueCounts[val] === 1) {
                  return "other";
                } else {
                  uniqueMultiValues[pollId].push(val);
                  return val;
                }
              });

              uniqueMultiValues[pollId] = [
                ...new Set(uniqueMultiValues[pollId]),
              ];

              console.log(
                `Processed Multi Value Map for Poll ID ${pollId}:`,
                multiValueMap[pollId]
              );
              console.log(
                `Unique Multi Values for Poll ID ${pollId}:`,
                uniqueMultiValues[pollId]
              );
            });

            $scope.uniqueMultiValues = uniqueMultiValues;

            if ($scope.hasStarType) {
              $scope.openValueMapping();
            }

            let uniqueStarValues = valueMappingService.getMappedValues();
            console.log("uniqueStarValues", uniqueStarValues);
            $scope.valuebox = true;
          },
        });
      };

      $scope.stopProcess = function () {
        $scope.isProcessing = false;
        console.log("stop processing is working");
      };

      $scope.processData = async function () {
        $scope.isProcessing = true;
        var csv = localStorage.getItem("fileContents");
        console.log("Get file is works properly", csv);

        if (!csv) {
          alert("No file contents found in local storage.");
          return;
        }
        $scope.showLoader = true;

        Papa.parse(csv, {
          complete: async function (results) {
            var lines = results.data;
            var headers = lines[0].map((header) =>
              header.trim().replace(/'/g, "")
            );
            var pollIds = lines[1].map((id) => id.trim());
            var types = lines[2].map((type) => type.trim());

            var result = headers.map((header, index) => ({
              question: header,
              answers: [],
              allAnswers: [],
              questionindex: index,
              pollId: pollIds[index],
              questionType: types[index],
              id: generateQuestionId(),
            }));

            for (var i = 3; i < lines.length; i++) {
              var values = lines[i];

              let guid = generateGUID();
              let guidForIp = generateGUID();
              let guidForFingerprint = generateGUID();

              let matrixdatastring = [];
              let matrixvalue = 0;

              if (values.length !== headers.length) {
                continue;
              }
              if (!$scope.isProcessing) {
                alertService.showSimpleToast("Processing stopped.");
                break;
              }

              for (let index = 0; index < values.length; index++) {
                let value = values[index].trim();
                if (result[index].questionType === "matrix") {
                  console.log("MatrixValueMap:", matrixValueMap);
    
                  if (!Array.isArray(matrixValueMap) || matrixValueMap.length === 0) {
                    console.error("MatrixValueMap is invalid or empty.");
                    continue;  // Skip to next index
                  }
    
                  // Iterate through each matrix entry (pollId)
                  for (const mapItem of matrixValueMap) {
                    const { pollId, rows, columns } = mapItem;
    
                    if (!rows || !columns || !pollId || !Array.isArray(rows) || !Array.isArray(columns)) {
                      console.error("MatrixValueMap item is missing rows, columns, or pollId.");
                      continue;
                    }
    
                    // Process each row and column for the current pollId
                    for (const row of rows) {
                      if (!row.trim()) continue;
                      for (const column of columns) {
                        if (!column.trim()) continue;
    
                        const userInput = values.some((val, idx) => {
                          return (
                            val.trim() === column.trim() &&
                            headers[idx]?.trim() === row.trim()
                          );
                        });
    
                        matrixdatastring.push({
                          pollId,
                          row: row.trim(),
                          column: column.trim(),
                          userInput: !!userInput,
                        });
                      }
                    }
    
                    matrixvalue = parseInt(pollId, 10); 
                  }
    
                  console.log("Generated Matrix Data String:", JSON.stringify(matrixdatastring));
                } else if (
                  (result[index].questionType !== "multi" &&
                    result[index].questionType !== "single") ||
                  !value.includes(",")
                ) {
                  value = $scope.valueMapping[value] || value;
                  value = value.trim().replace(/\.$/, "");
                  result[index].allAnswers.push(value);

                  if (value && !result[index].answers.includes(value)) {
                    result[index].answers.push(value);
                    createAnswer(
                      value,
                      result[index].pollId,
                      result[index].questionType
                    );
                  }
                } else {
                  let splitValues = value.split(",").map((v) => v.trim());
                  splitValues.forEach((val) => {
                    val = $scope.valueMapping[val] || val;
                    val = val.trim().replace(/\.$/, "");
                    result[index].allAnswers.push(val);

                    if (val && !result[index].answers.includes(val)) {
                      result[index].answers.push(val);
                      createAnswer(
                        val,
                        result[index].pollId,
                        result[index].questionType
                      );
                    }
                  });
                }
              }

              for (const item of result) {
                const skipValues = ["n/a", "none", "na"];
                for (let answer of item.allAnswers) {
                  if (answer !== "") {
                    if (item.questionType == "single") {
                      item.questionType = "selectable";
                    }
                    if (item.questionType == "multi") {
                      let pollId = item.pollId;
                      let uniqueValues = $scope.uniqueMultiValues[pollId] || [];

                      let normalizedUniqueValues = uniqueValues.map((val) =>
                        val.trim().toLowerCase()
                      );
                      let normalizedAnswer = answer.trim().toLowerCase();

                      if (!normalizedUniqueValues.includes(normalizedAnswer)) {
                        answer = "other";
                      }
                    }
                    if (skipValues.includes(answer.toLowerCase())) {
                      continue;
                    }

                    var data = {
                      nextQOrder: 0,
                      minDate: "",
                      scalevalues: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                      scaleselectedvalue: 0,
                      hasbrandofchoice: false,
                      loadAll: false,
                      page_number: 0,
                      hashtags: ["#general"],
                      eventid: $scope.idInput,
                      geodata: {},
                      guid: guid,
                      isPublic: "1",
                      ec: "",
                      type: "Survey",
                      loggedin: false,
                      browser: {},
                      extractedfrom: null,
                      qtype: item.questionType,
                      pollid: item.pollId,
                      isMobile: false,
                      vote: answer,
                      voteid: 0,
                      ip: guidForIp,
                      fingerprint: guidForFingerprint,
                      useragent: "",
                      multisubmission: null,
                      label: "",
                      latitude: $scope.latitude,
                      longitude: $scope.longitude,
                      isfirst: false,
                      ticketid: "",
                    };
                    console.log("poll data ", data);
                    try {
                      let response = $http.post(
                        "https://crowdsnap-api.azurewebsites.net/api/poll/vote",
                        data
                      );

                      $scope.apiCallCount++;
                      $scope.apiCallCount2++;

                      if ($scope.apiCallCount2 > 499) {
                        $scope.apiCallCount2 = 0;
                        await new Promise((resolve) =>
                          setTimeout(resolve, 15000)
                        );
                      }

                      if (response.data === "" && $scope.apiCallCount < 4) {
                        alert("Please Check Entered Event Id");
                        $scope.apiCallCount = 0;
                        $scope.isProcessing = false;
                        throw new Error("Please Check Entered Event Id");
                      }

                      $scope.apiCallCount++;
                      console.log("API response:", response.data);
                    } catch (error) {
                      console.error("API error:", error);
                      alert(error.message);
                      $scope.showLoader = false;
                      return;
                    }
                  }
                }
                item.allAnswers = [];
              }
          for (const mapItem of matrixValueMap) {
            const { pollId, rows, columns } = mapItem;

            const currentMatrixData = matrixdatastring.filter(item => item.pollId === pollId);

            if (currentMatrixData.length === 0) {
              console.warn(`No matrix data found for pollId: ${pollId}. Skipping.`);
              continue;
            }

            const matrixPayload = {
              clientid: "",
              loadAll: false,
              page_number: 0,
              userdid: "",
              scaleselectedvalue: 0,
              question_description: "",
              question: "",
              inputs: [],
              pollid: pollId, 
              qorder: 1,
              qtype: "matrix",
              label: "",
              voteresults: { v: [] },
              checkduplicate: "false",
              matrixtype: "multi",
              hasbrandofchoice: false,
              hashtags: ["#general"],
              pollends: new Date().toISOString(),
              eventid: $scope.idInput,
              geodata: {},
              guid: guid,
              isPublic: "1",
              ec: "KCEV" + $scope.idInput,
              type: "Survey",
              loggedin: true,
              browser: {},
              extractedfrom: null,
              isMobile: false,
              vote: JSON.stringify(currentMatrixData),
              voteid: null,
              ip: guidForIp,
              fingerprint: guidForFingerprint,
              useragent: "",
              multisubmission: null,
              isfirst: true,
              backbutton: null,
            };

            try {
              const response = await $http.post(
                "https://crowdsnap-api.azurewebsites.net/api/poll/vote",
                JSON.stringify(matrixPayload)
              );
              console.log("Matrix API Response for pollId " + pollId + ":", response.data);
              
          $scope.apiCallCount++;
            } catch (error) {
              console.error("Matrix API Error for pollId " + pollId + ":", error);
              alert(error.message);
              $scope.showLoader = false;
              return;
            }
          }
              if ($scope.apiCallCount > 499) {
                await new Promise(resolve => setTimeout(resolve, 15000));
              }
              $scope.responses++;

              let cc = {
                eventid: $scope.idInput,
                sessionid: guid,
                status: 1,
              };
              let r = await $http.post(
                "https://crowdsnap-api.azurewebsites.net/api/poll/campaigncompleted",
                cc
              );
              console.log("completed event", r);
            }

            var jsonarray = {
              name: "General questions",
              description: "code testing questions and answers",
              category: "General",
              survey: result,
              code: generateGUID(),
              userid: "f4b29520-f216-11ed-bfe6-d97200d4cd7d",
            };

            try {
              // let response = await $http.post('https://crowdsnap.ai/api/polls/savegpt', jsonarray);
              // console.log(response.data);
            } catch (error) {
              console.error(error);
            }
            $scope.showLoader = false;
            $scope.showFinal = true;
            $scope.$apply();
            if ($scope.isProcessing) {
              alertService.showSimpleToast("All data uploaded successfully!");
            }
          },
        });
      };

      function generateGUID() {
        var s4 = function () {
          return Math.floor((1 + Math.random()) * 0x10000)
            .toString(16)
            .substring(1);
        };
        return (
          s4() +
          s4() +
          "-" +
          s4() +
          "-" +
          s4() +
          "-" +
          s4() +
          "-" +
          s4() +
          s4() +
          s4()
        );
      }

      function generateQuestionId() {
        return Math.floor(10000000 + Math.random() * 90000000).toString();
      }

      async function createAnswer(val, pollId, questionType) {
        if (val !== "" || questionType !== "text" || questionType !== "star") {
          var data = {
            poption: val,
            extractedfrom: 0,
            pollid: pollId,
            qtype: questionType,
            existing_option_id: 0,
          };
          if (data.qtype == "multi" || data.qtype == "single") {
            console.log("Poll option data - ", data);

            try {
              let response = await $http.post(
                "https://crowdsnap.ai/api/poll/addclient_poll_option",
                data
              );
              console.log("API response:", response.data);
            } catch (error) {
              console.error("API error:", error);
            }
          }
        }
      }
    },
  ])

  .directive("ngDrop", function () {
    return {
      link: function (scope, element, attrs) {
        element.on("dragover", function (event) {
          event.preventDefault();
          event.stopPropagation();
          scope.$apply(function () {
            scope.isDragOver = true;
          });
        });

        element.on("dragleave", function (event) {
          event.preventDefault();
          event.stopPropagation();
          scope.$apply(function () {
            scope.isDragOver = false;
          });
        });

        element.on("drop", function (event) {
          event.preventDefault();
          event.stopPropagation();
          scope.$apply(function () {
            scope.isDragOver = false;
            scope.onFileDrop(event);
          });
        });
      },
    };
  });
