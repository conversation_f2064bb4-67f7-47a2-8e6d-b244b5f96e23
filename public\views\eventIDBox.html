<md-dialog>
  <md-dialog-content style="display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
    <h2 style="color: #fff !important;" ng-hide="activities.length > 0">Enter Event ID</h2>
    <!-- <h2 style="color: #fff !important;" ng-show="activities.length > 0">Select An Activity</h2> -->
    
    <div style="display: flex; gap: 10px;">
      <input type="number" ng-model="eventid" style="width: 180px; text-align: center; letter-spacing: 8px; font-size: 18px;" required>
    </div>    

    <div ng-show="errorMessage" class="error-message">
      <p>{{ errorMessage }}</p>
    </div>
    
    <!-- <div ng-show="showDropdown" class="dropdown-wrapper" ng-class="{'_open': dropdownOpen}">
      <button class="dropdown" ng-click="toggleDropdown()">
        {{ selectedActivity.name || 'Select an Activity' }}
      </button>
      <nav class="dropdown-list" style="position: relative !important;">
        <a href="#" 
           class="dropdown-list__item" 
           ng-repeat="activity in activities"
           ng-click="selectActivityItem(activity); $event.preventDefault()">
          {{activity.name}}
        </a>
      </nav>
    </div> -->
    
  </md-dialog-content>

  <md-dialog-actions style="padding-left: 10px !important; padding-right: 10px !important;">
    <img src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px;" ng-show="loading">
    
    <md-button class="btn-body sec-button md-button ng-scope md-ink-ripple" ng-click="submitEventID()" ng-hide="activities.length > 0">Submit</md-button>
    
    <!-- <md-button class="btn-body sec-button md-button ng-scope md-ink-ripple" ng-click="selectActivity()" ng-show="activities.length > 0">Submit</md-button> -->
  </md-dialog-actions>
</md-dialog>