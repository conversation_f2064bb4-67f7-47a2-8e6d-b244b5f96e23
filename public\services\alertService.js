angular.module('myApp').service('alertService', function alertService($mdToast) {
    var service = {};
    service.showSimpleToast = function(msg,hideDelay) {
        let hd = 5000;
        if(hideDelay){
          hd=hideDelay;
        }
  
   $mdToast.show(
  
            $mdToast.simple()
              .textContent(msg)
              .hideDelay(hd)
              .position('top right')
              .toastClass('custom-toast') 
  
          ).then(function() {
            // $log.log('Toast dismissed.');
          }).catch(function() {
            $log.log('Toast failed or was forced to close early by another toast.');
          });
          
        };

    return service;
});