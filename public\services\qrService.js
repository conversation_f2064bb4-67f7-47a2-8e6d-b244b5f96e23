angular.module('myApp')
  .service('qrService', function($http, $window, $rootScope) {
    const verifyUrl = 'https://crowdsnap-api.azurewebsites.net/api/polls/verifyparticipant';
    const verifyParticipantUrl = 'https://crowdsnap-api.azurewebsites.net/api/polls/isparticipantverified';
    const activityUrl = 'https://crowdsnap-api.azurewebsites.net/api/polls/getsurvey';
    const VoteUrl = 'https://crowdsnap-api.azurewebsites.net/api/poll/vote';
    const token = $window.localStorage.getItem("token");
    let pollid = '';
    let surveyDataEntrance = {};
    let surveyData = {};

    this.verifyParticipant = function(eventid, guid) {
      const payload = {
        eventid: eventid,
        guid: guid
      }; 

      return $http.post(verifyUrl, JSON.stringify(payload), {headers: { Authorization: token} })
        .then(function(response) {
          return response.data;
        })
        .catch(function(error) {
          console.log("Logout triggered!");
          $rootScope.$broadcast("logoutEvent");
          console.error('Error in verifying participant:', error);
          throw error;
        });
    };

    this.verifyParticipantActivity = function(eventid, guid) {

      const payload = {
        eventid: eventid,
        guid: guid,
        subtype :'verification',
      }; 

      return $http.post(verifyParticipantUrl, JSON.stringify(payload), {headers: { Authorization: token} })
        .then(function(response) {
          return response.data;
        })
        .catch(function(error) {
          console.log("Logout triggered!");
          $rootScope.$broadcast("logoutEvent");
          console.error('Error in verifying participant:', error);
          throw error;
        });
    };

    this.loadActivities = function(eventid) {
      return $http.post(activityUrl, JSON.stringify({
          eventcode: "KCEV" + eventid,
          userdid: "f4b29520-f216-11ed-bfe6-d97200d4cd7d",
          eventid: eventid
      }), { headers: { Authorization: token } })
      .then(function(response) {
          if (response.data && response.data.survey && Array.isArray(response.data.survey.survey) && response.data.survey.survey.length > 0) {
              surveyData = response.data.survey.survey[0];
              surveyDataEntrance = response.data.survey.survey[1];
              
              if (!surveyData.pollid || !Array.isArray(surveyData.options)) {
                  console.warn("Incorrect response structure, returning dummy values.");
                  return { id: 0, name: "activities", mapping: 12345 };
              }
  
              return surveyData.options.map(option => ({
                  id: option.id ,
                  name: option.poption ,
                  mapping: option.mapping 
              }));
          } else {
              console.error("Empty or invalid response received.");
              throw new Error("Failed to load activities. ");
          }
      })
      .catch(function(error) {
          console.error("Error in Load Activities participant:", error);
          throw error;
      });
  };
  
  function generateGUID() {
    var s4 = function () {
      return Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
    };
    return (
      s4() +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      s4() +
      s4()
    );
  }
    this.saveVote = function(eventid, guid, selectedActivity, index) {
      if (index == 1) {
        pollid = surveyDataEntrance.pollid;
        const loadActivity = surveyDataEntrance.options.find(
          (activity) => activity.poption === "Entrance"
        );
        console.log( loadActivity);
        selectedActivity = loadActivity;
        vote = selectedActivity.poption;
      }else {
        vote = selectedActivity.name;
        pollid = surveyData.pollid;
      }
      var votePayload = {
        nextQOrder: 0,
        minDate: "",
        scalevalues: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        scaleselectedvalue: 0,
        hasbrandofchoice: false,
        loadAll: false,
        page_number: 0,
        hashtags: ["#general"],
        eventid: parseInt(eventid, 10),
        geodata: {},
        guid: guid,
        isPublic: "1",
        ec: "",
        type: "Survey",
        loggedin: false,
        browser: {},
        extractedfrom: null,
        qtype: "selectable_radio",
        pollid: pollid,
        isMobile: false,
        vote: vote,  
        voteid: selectedActivity.id,
        ip: generateGUID(),
        fingerprint: generateGUID(),
        useragent: "",
        multisubmission: null,
        label: "",
        latitude: "",
        longitude: "",
        isfirst: false,
        ticketid: "",
      };

      console.log("Survey votePayload:", votePayload);

      return $http.post(VoteUrl, JSON.stringify(votePayload), {headers: { Authorization: token} })
        .then(function(response) {
          return response.data.status;
        })
        .catch(function(error) {
          console.error('Error in verifying participant:', error);
          throw error;
        });
    };
  });

