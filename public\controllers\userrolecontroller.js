angular
  .module("myApp")
  .controller(
    "UserRoleController",
    function ($scope, $http, $mdDialog, $window, alertService) {
      $scope.usertasks = {};
      $scope.addUserRoll = {};
      $scope.addMember = {};
      $scope.isLoading = true;
      $scope.isLoadingAnimation = false;
      $scope.showLoader = false;
      $scope.token = $window.localStorage.getItem("token");
      $scope.isLoggedIn = $window.localStorage.getItem("isLoggedIn");
      console.log("is logged in", $scope.isLoggedIn);
      $scope.errorMessage = "";
      $scope.selectedRaffleDraw = [];
      $scope.showWinners = false;
      $scope.eventid = $window.localStorage.getItem("eventid");
      

      $scope.formatDateTime = function (dateTime) {
        if (!dateTime) return null;

        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = ("0" + (date.getMonth() + 1)).slice(-2); // Ensure 2 digits
        const day = ("0" + date.getDate()).slice(-2); // Ensure 2 digits
        const hours = ("0" + date.getHours()).slice(-2); // Ensure 2 digits
        const minutes = ("0" + date.getMinutes()).slice(-2); // Ensure 2 digits
        const seconds = ("0" + date.getSeconds()).slice(-2); // Ensure 2 digits

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      $http
        .get("/getSurveys")
        .then(function (response) {
          $scope.activity = response.data;
          console.log("Activity data:", $scope.activity);
          $scope.isLoading = false;
        })
        .catch(function (error) {
          console.error("Error fetching user rolls:", error);
          $scope.isLoading = false;
        });

      $scope.isFormValid = function () {
        return $scope.email;
      };

      $scope.addMember = function (surveyId) {
        if (!$scope.isFormValid()) {
          return;
        }
        $scope.assignMember = {
          survey_id: surveyId,
          user_email: $scope.email,
          user_note: $scope.note,
        };

        console.log("User Data:", $scope.assignMember);

        $http
          .post("/assignmember", $scope.assignMember)
          .then(function (response) {
            console.log("Response from server:", response.data);
            alertService.showSimpleToast("Service Agent Allocated");
          })
          .catch(function (error) {
            console.error("Error sending data to server:", error);
            alertService.showSimpleToast("Agent Allocate is Failed");
          });
        $mdDialog.hide();
      };

      $scope.addCredits = function (userId, credits) {
        $scope.addcredits = {
          userid: userId,
          creditsAmount: credits,
        };

        $http
          .post("/addCredits", $scope.addcredits)
          .then(function (response) {
            if (response.data.success) {
              alertService.showSimpleToast("Credits added");
            } else {
              alertService.showSimpleToast("Failed to adding credits");
            }
          })
          .catch(function (error) {
            console.error("Error adding user:", error);
            alertService.showSimpleToast("Error connecting to server.");
          });
      };

      $scope.addUser = function (username, password) {
        $scope.adduser = {
          username: username,
          password: password,
        };

        $http
          .post("/addUser", $scope.adduser)
          .then(function (response) {
            if (response.data.success) {
              alertService.showSimpleToast("User added successfully.");
            } else {
              alertService.showSimpleToast("Failed to add user.");
            }
          })
          .catch(function (error) {
            console.error("Error adding user:", error);
            alertService.showSimpleToast("Error connecting to server.");
          });
      };

      $scope.getRandomVote = function (pollid, startTime, endTime) {
        if (!pollid || !startTime || !endTime) {
          alert("All fields are required!");
          return;
        }
        $scope.randomVote = {
          eventid: $scope.eventid,
          pollid: pollid,
          startTime: $scope.formatDateTime(startTime),
          endTime: $scope.formatDateTime(endTime),
        };
        console.log("random Vote data ", $scope.randomVote);
        $scope.showLoader = true;

        $http
          .post(
            "https://www.crowdsnap.ai/api/poll/getRandomVoteByTimeRange",
            $scope.randomVote,
            { headers: { Authorization: $scope.token } }
          )
          .then(function (response) {
            $scope.showLoader = false;
            if (response !== null) {
              const selection = response.data;
              console.log("response", selection);

              $scope.showSelectedDraw(selection);
            } else {
              alertService.showSimpleToast("Failed to Selecting Raffle Draw");
            }
          })
          .catch(function (error) {
            console.error("Error selecting Vote", error);
            alertService.showSimpleToast("Error connecting to server.");
          });
      };

      $scope.showSelectedDraw = function (selection) {
        $mdDialog.show({
          controller: function DialogController($scope, $mdDialog, number) {
            $scope.selection = number;
            $scope.isNull = !number;
            $scope.isLoadingAnimation = true;
            $scope.animatedNumber = 0;
            let interval;

            function startRollingAnimation() {
              interval = setInterval(() => {
                $scope.animatedNumber = Math.floor(Math.random() * 100000000);
                $scope.$apply();
              }, 200);
            }

            function stopRollingAnimation() {
              alertService.showSimpleToast("Raffle Draw Selected");
              clearInterval(interval);
              $scope.isLoadingAnimation = false;
              $scope.$apply();
            }

            startRollingAnimation();

            setTimeout(stopRollingAnimation, 4000);

            $scope.closeDialog = function () {
              $mdDialog.hide();
            };
          },
          templateUrl: "./views/selectedDraw.html",
          parent: angular.element(document.body),
          clickOutsideToClose: true,
          locals: {
            number: selection,
          },
        });
      };

      $scope.selectedRaffleDraw = []; // Initialize array to hold winners

      $scope.openSelections = function (clientid) {
        // if (!clientid) {
        //   alert("Survey ID is required!");
        //   return;
        // }

        // Simulate sample data
        const sampledata = {
          status: "success",
          data: [
            "753567890",
            "455949855",
            "987654321",
            "123456789",
            "567890123",
          ],
        };

        // $scope.selectedRaffleDraw = sampledata.data;

        $http
          .post(
            "https://www.crowdsnap.ai/api/poll/geteventmq_all",
            { subtype: "raffle", eventid: parseInt($scope.eventid, 10) },
            {
              headers: { Authorization: $scope.token },
            }
          )
          .then(function (response) {
            $scope.showWinners = true;
            $scope.selectedRaffleDraw = response.data
              .map(function (item) {
                const metadata = JSON.parse(item.metadata);

                if (metadata.datavalue) {
                  return {
                    datavalue: metadata.datavalue,
                    participant_name: metadata.participant_name || "N/A",
                    startTime: metadata.startTime,
                    endTime: metadata.endTime,
                  };
                }
                return null;
              })
              .filter(function (item) {
                return item !== null;
              });
          })
          .catch(function (error) {
            console.error("Error fetching data:", error);
            alert("Failed to fetch data. Please try again.");
          });
      };

      $scope.parseMetadata = function (value) {
        return JSON.parse(value.metadata || "{}");
      };
    }
  );
