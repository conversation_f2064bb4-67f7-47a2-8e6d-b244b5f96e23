<div ng-controller="UserRoleController" layout="column" class="analytics-container" ng-show="isLoggedIn">
    <md-tabs md-stretch-tabs class="md-primary" md-selected="data.selectedIndex">
        <md-tab id="tab1" aria-controls="tab1-content">Find the winner</md-tab>
        <md-tab id="tab2" aria-controls="tab2-content">Winner list</md-tab>
    </md-tabs>

    <md-content flex md-scroll-y style="display: block !important;">
        <ui-view layout="column" layout-fill layout-padding>
            <div class="inset" hide-sm></div>
            <ng-switch on="data.selectedIndex" class="tabpanel-container">
                <!-- Tab 1: Find The Winner -->
                <div role="tabpanel" id="tab1-content" aria-labelledby="tab1" ng-switch-when="0" md-swipe-left="next()" md-swipe-right="previous()" layout="column" layout-align="center center">
                    
                        <!-- <md-input-container style="width: 100%; margin-bottom: 20px;">
                            <md-icon md-svg-src="assets/images/user.svg"></md-icon>
                            <input class="inputBox" type="text" placeholder="Survey Id" ng-model="clientid" required>
                            <div ng-messages="form.clientid.$error">
                                <div ng-message="required">Survey ID is required.</div>
                            </div>
                        </md-input-container> -->

                        <md-input-container style="width: 100%; margin-bottom: 20px;">
                            <md-icon md-svg-src="assets/images/hash.svg"></md-icon>
                            <input class="inputBox" type="text" placeholder="Poll ID" ng-model="pollid" required>
                            <div ng-messages="form.pollid.$error">
                                <div ng-message="required">Poll ID is required.</div>
                            </div>
                        </md-input-container>

                        <md-input-container style="width: 100%; margin-bottom: 20px;">
                            <md-icon md-svg-src="assets/images/time.svg"></md-icon>
                            <label>Select Start Time</label>
                            <input type="datetime-local" ng-model="startTime" required>
                            <div ng-messages="form.startTime.$error">
                                <div ng-message="required">Start Time is required.</div>
                            </div>
                        </md-input-container>

                        <md-input-container style="width: 100%; margin-bottom: 20px;">
                            <md-icon md-svg-src="assets/images/time.svg"></md-icon>
                            <label>Select End Time</label>
                            <input type="datetime-local" ng-model="endTime" required>
                            <div ng-messages="form.endTime.$error">
                                <div ng-message="required">End Time is required.</div>
                            </div>
                        </md-input-container>

                        <img ng-if="showLoader" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px;">
                        <md-button ng-click="getRandomVote(pollid, startTime, endTime)" class="md-raised md-primary" style="width: 100%; max-width: 200px; background-color: #333; color: white !important;">
                            Submit
                        </md-button>
                    
                </div>

                <!-- Tab 2: Winners List -->
                <div role="tabpanel" id="tab2-content" aria-labelledby="tab2" ng-switch-when="1" layout="column" layout-align="center center">
                    
                    <!-- <md-input-container style="width: 100%; margin-bottom: 20px;" >
                        <md-icon md-svg-src="assets/images/user.svg"></md-icon>
                        <input class="inputBox" type="text" placeholder="Survey Id" ng-model="clientid" required>
                    </md-input-container> -->
                    
                    <md-button ng-click="openSelections(clientid)" class="md-raised md-primary" 
                               style="width: 100%; max-width: 200px; background-color: #333; color: white !important;">
                        Show Winners
                    </md-button>
                    
                    <!-- Winner List -->
                    <div ng-if="selectedRaffleDraw.length > 0" style="margin-top: 20px; text-align: left; width: 100%; max-width: 400px; justify-items: center;">
                        <h3 style="color: #313A42 !important; font-size: 40px !important; padding-bottom: 20px !important;">Winners</h3>
                        <div class="numberlist" ng-repeat="value in selectedRaffleDraw track by $index">
                            <label style="width: 100%; font-size: 20px; display: flex; align-items: center;">
                                <span style="width: 30px; text-align: left;">{{ $index + 1 }}.</span>
                                <span 
                                  style="text-align: left;" 
                                  title="{{ value.startTime }} - {{ value.endTime }}"
                                >
                                  0{{ value.datavalue }}
                                </span>
                                <span 
                                  style="text-align: left;" 
                                  title="{{ value.startTime }} - {{ value.endTime }}"
                                >
                                  - {{ value.participant_name }}
                                </span>
                              </label>
                              
                          </div>
                          
                    </div>
                </div>
                
            </ng-switch>
        </ui-view>
    </md-content>
</div>
