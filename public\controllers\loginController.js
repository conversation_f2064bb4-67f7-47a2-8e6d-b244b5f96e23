angular.module("myApp").controller("LoginController", [
  "$scope",
  "$window",
  "$http",
  "$mdDialog",
  "$timeout",
  "alertService",
  "$rootScope",
  "$location",
  function (
    $scope,
    $window,
    $http,
    $mdDialog,
    $timeout,
    alertService,
    $rootScope,
    $location
  ) {
    $scope.userName = localStorage.getItem("username");

    $scope.loginData = {
      username: "",
      password: "",
    };
    $scope.email = "";

    $rootScope.$on("logoutEvent", function () {
      console.log("logout rootscope working now");
      $scope.logout();
    });

    $scope.isPasswordVisible = false;
    $scope.showLoginLoader = false;

    $scope.isLoggedIn = $window.localStorage.getItem("isLoggedIn") === "true";
    $scope.isAdmin = $window.localStorage.getItem("isAdmin") === "753";
    $scope.error = false;
    $scope.errorMessage = "";
    $scope.showOtpInput = false;
    $scope.errorMessage = "";
    $scope.otp = "";

    let autoLogoutTimer;

    $scope.login = function () {
      $scope.showLoginLoader = true;
      $scope.email = $scope.loginData.username;
      $scope.authdata = {
        email: $scope.loginData.username,
        password: $scope.loginData.password,
        issocial: true,
      };

      $http
        .post("https://www.crowdsnap.ai/api/client/auth", $scope.authdata)
        .then(function (response) {
          if (response.data !== null) {
            $scope.showOtpDialog(response.data.userid2);
            $scope.showOtpInput = true;
            $scope.showLoginLoader = false;
            $scope.error = false;
          } else {
            $scope.error = true;
            $scope.errorMessage = "Login Failed";
          }
        })
        .catch(function (error) {
          console.error("Error logging in:", error);
          $scope.error = true;
          $scope.errorMessage = "Error connecting to server.";
        });
    };

    $scope.showOtpDialog = function (userid) {
      console.log("show otp is working");
      $mdDialog
        .show({
          controller: "OtpController",
          templateUrl: "./views/otpBox.html",
          parent: angular.element(document.body),
          clickOutsideToClose: false,
          locals: { userid: userid },
        })
        .then(
          function (result) {
            if (result === "success") {
              $scope.loginData = {
                username: "",
                password: "",
              };
              // $scope.startLogoutTimer();
              if (
                $scope.email == "<EMAIL>" ||
                $scope.email == "<EMAIL>"
              ) {
                $window.localStorage.setItem("isAdmin", "753");
                $window.location.href = "#!/home";
                window.location.reload();
              } else {
                $window.localStorage.setItem("isAdmin", "false");
                $window.location.href = "#!/";
                window.location.reload();
              }
              $scope.isLoggedIn = true;
              alertService.showSimpleToast("Successfully logged in.");
              $window.localStorage.setItem("isLoggedIn", "true");
            } else {
              $scope.error = true;
              $scope.errorMessage = "Invalid OTP";
            }
          },
          function () {
            console.log("OTP dialog was cancelled.");
          }
        );
    };

    // $scope.login = function() {
    //     $http.post('/login', $scope.loginData)
    //         .then(function(response) {
    //             if (response.data.success) {
    //                 $scope.isLoggedIn = true;
    //                 $scope.error = false;

    //                 $window.localStorage.setItem('isLoggedIn', 'true');
    //                 $window.localStorage.setItem('username', $scope.loginData.username);
    //                 alertService.showSimpleToast('Successfully logged in.');
    //                 window.location.reload();
    //             } else {
    //                 $scope.error = true;
    //                 $scope.errorMessage = 'Invalid username or password.';
    //             }
    //         })
    //         .catch(function(error) {
    //             console.error("Error logging in:", error);
    //             $scope.error = true;
    //             $scope.errorMessage = 'Error connecting to server.';
    //         });
    // };

    $scope.startNew = function () {
      console.log("Start new button is working");
      window.location.reload();
    };

    $scope.refreshPage = function () {
      window.location.reload();
    };

    $scope.togglePassword = function () {
      $scope.isPasswordVisible = !$scope.isPasswordVisible;
      var passwordField = document.getElementById("pass");
      passwordField.type = $scope.isPasswordVisible ? "text" : "password";
    };

    $scope.checkLogoutTime = function () {
      const logoutTime = parseInt(
        $window.localStorage.getItem("logoutTime"),
        10
      );
      if (logoutTime && Date.now() >= logoutTime) {
        $scope.logout();
        alertService.showSimpleToast(
          "You have been logged out due to inactivity."
        );
      } else if (logoutTime) {
        const remainingTime = logoutTime - Date.now();
        autoLogoutTimer = $timeout(function () {
          $scope.logout();
          alertService.showSimpleToast(
            "You have been logged out due to inactivity."
          );
        }, remainingTime);
      }
    };

    // $scope.checkLogoutTime();

    // $scope.startLogoutTimer = function() {
    //     console.log("Starting logout timer...");

    //     const logoutTime = Date.now() + 50*60*1000;
    //     $window.localStorage.setItem('logoutTime', logoutTime);

    //     autoLogoutTimer = $timeout(function() {
    //         $scope.logout();
    //         alertService.showSimpleToast('You have been logged out due to inactivity.');
    //     },50*60*1000);
    // };

    $scope.logout = function () {
      if (autoLogoutTimer) {
        $timeout.cancel(autoLogoutTimer);
      }
      $scope.isLoggedIn = false;
      $window.location.href = "#!/login";
      location.reload();
      $window.localStorage.clear();
    };

    window.addEventListener("beforeunload", function () {
      console.log("Tab/Window is closing, clearing localStorage...");
      if (!token) {
        console.error("Authorization token not found.");
        $rootScope.$broadcast("logoutEvent");
        return;
      }
    });
    $rootScope.isConference = $window.localStorage.getItem("isConference");
  },
]);
