<div ng-controller="MainController" layout="column" class="analytics-container" layout-fill role="main" ng-show="isLoggedIn">
  <md-content flex md-scroll-y ng-controller="QRController">
    <!-- Toggle between Entrance and Activity -->
    <div class="toggle-container">
      <button ng-click="toggleScanMode('event')" ng-class="{'active': selectedMode === 'event'}">Entrance</button>
      <button ng-click="toggleScanMode('activities')" ng-class="{'active': selectedMode === 'activities'}">Activity</button>
    </div>

<!-- Activity Select Dropdown -->
 
<div class="activity-select-wrapper" ng-show="selectedEvent.name === 'activities'">
  <div class="dropdown-wrapper" ng-class="{'_open': activityDropdownOpen}" style="z-index: 1000;">
    <button class="dropdown" ng-click="toggleActivityDropdown()">
      {{ selectedActivity.name || 'Select an activity' }}
    </button>
    <nav class="dropdown-list">
      <a href="#" 
         class="dropdown-list__item" 
         ng-repeat="activity in activities"
         ng-click="selectActivityItem(activity); $event.preventDefault()">
        {{activity.name}}
      </a>
    </nav>
  </div>
</div>

    <!-- Camera Scanner -->
    <div ng-show="isScannerOpen" class="scanner-container">
      <video id="video" class="video-element" autoplay playsinline></video>
      <canvas id="canvas" width="720" height="720" style="width: 360px; height: 360px; display: none;"></canvas>

    </div>    

    <div ng-show="scannedData" class="notification-container">
      <!-- Success Message -->
      <div class="notification success" ng-show="isVerified === 1">
        <div class="close-icon" ng-click="done()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        
        <div class="participant-details">
          <h3 class="participant-name">{{name}}</h3>
          <p class="participant-info">{{designation}}</p>
          <p class="participant-info">{{company}}</p>
        </div>
        
        <div class="status-header">
          <div class="icon-circle" ng-click="done()">
            <i class="checkmark">✓</i>
          </div>
          <h2>Yeah!</h2>
          <p class="status-message">{{scannedData}}</p>
        </div>
        
        <button class="action-button" ng-click="done()">Done</button>
      </div>

      <!-- Error Message -->
      <div class="notification error" ng-show="isVerified === -1">
        <div class="close-icon" ng-click="done()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        <div class="participant-details">
          <h3 class="participant-name">{{name}}</h3>
          <p class="participant-info">{{designation}}</p>
          <p class="participant-info">{{company}}</p>
        </div>
        <div class="status-header">
          <div class="icon-circle" ng-click="done()">
            <!-- <i class="cross">✕</i> -->
            <svg xmlns="http://www.w3.org/2000/svg" width="54" height="54" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <h2>Oh no!</h2>
          <p class="status-message">{{scannedData}}</p>
        </div>
        <button class="action-button" ng-click="done()" style="background: rgb(255, 181, 181) !important; color: #313A42;">Tap to Retry</button>
      </div>

      <div class="notification error" ng-show="isVerified === 0 ">
        <div class="close-icon" ng-click="done()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        <div class="status-header">
          <div class="icon-circle" ng-click="done()">
            <!-- <i class="cross">✕</i> -->
            <svg xmlns="http://www.w3.org/2000/svg" width="54" height="54" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <h2>Oh no!</h2>
          <p class="status-message">{{scannedData}}</p>
        </div>
        <button class="action-button" ng-click="done()" style="background: rgb(255, 181, 181) !important; color: #313A42;">Tap to Retry</button>
      </div>

      <div class="notification error" ng-show="isVerified === 404">
        <div class="close-icon" ng-click="done()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        <div class="status-header">
          <div class="icon-circle" ng-click="done()">
            <svg xmlns="http://www.w3.org/2000/svg" width="54" height="54" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <p class="status-message" style="font-size: 18px !important; color: red !important">{{scannedData}}</p>
        </div>
        <button class="action-button" ng-click="done()" style="background: rgb(229, 229, 229) !important; color: #313A42;">OK</button>
      </div>
    </div>

    <!-- Display Scanned Data -->
    <div ng-if="scannedData">
      <!-- <p>{{ scannedData }}</p> -->
      <!-- <h4>QR Code Image:</h4> -->
      <img ng-src="{{ qrImage }}" alt="Scanned QR Image" width="auto" height="200"/>
    </div>
  </md-content>
</div>
