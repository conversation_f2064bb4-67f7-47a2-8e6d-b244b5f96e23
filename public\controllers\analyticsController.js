angular.module('myApp').controller("AnalyticsController", ["$scope", "$mdDialog", "$http", "$window", "$document", function ($scope, $mdDialog, $http, $window, $document) {
    
  // Dropdown Settings
  $scope.items = ["Survey ID", "User Email", "User ID"];
  $scope.selectedText = "Survey ID";
  $scope.searchPlaceholder = "Search in Survey ID...";
  $scope.dropdownVisible = false;
  $scope.iconRotation = "rotate(0deg)";

  // Dropdown Toggle
  $scope.toggleDropdown = function() {
      $scope.dropdownVisible = !$scope.dropdownVisible;
      $scope.iconRotation = $scope.dropdownVisible ? "rotate(180deg)" : "rotate(0deg)";
  };

  // Select Item and Update Placeholder
  $scope.selectItem = function(item) {
      $scope.selectedText = item;
      $scope.searchPlaceholder = item === "Everything" ? "Search Anything..." : `Search in ${item}...`;
      $scope.dropdownVisible = false;
      $scope.iconRotation = "rotate(0deg)";
  };

  // Close Dropdown When Clicking Outside
  $scope.closeDropdownOnClickOutside = function(event) {
      if (!event.target.closest('.dropdown')) {
          $scope.$apply(() => {
              $scope.dropdownVisible = false;
              $scope.iconRotation = "rotate(0deg)";
          });
      }
  };

  // Attach event listener for closing dropdown
  angular.element($document).on('click', $scope.closeDropdownOnClickOutside);

  // Cleanup listener on scope destroy
  $scope.$on('$destroy', function() {
      angular.element($document).off('click', $scope.closeDropdownOnClickOutside);
  });


  // Show Add User Dialog with Parameters
  $scope.showAddUser = function(ev, userRollId, userRoll) {
      console.log("Clicked button member:", userRollId);
      $mdDialog.show({
          controller: DialogController,
          templateUrl: "views/assignmember.html",
          targetEvent: ev,
          locals: { userRollId: userRollId, userRoll: userRoll },
          clickOutsideToClose: false
      });
  };


  function DialogController($scope, $mdDialog, userRoll, userRollId) {
      $scope.userRoll = userRoll;
      $scope.userRollId = userRollId;

      $scope.hide = function() { $mdDialog.hide(); };
      $scope.cancel = function() { $mdDialog.cancel(); };
      $scope.answer = function(answer) { $mdDialog.hide(answer); };
  }
}]);
