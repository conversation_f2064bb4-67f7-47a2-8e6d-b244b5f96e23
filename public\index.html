<!DOCTYPE html>
<html class="no-js" lang="en">

<head>
  <title>CrowdSnap</title>

  <meta name="viewport" content="width=device-width, initial-scale=0.8">
  <link rel="stylesheet" href="./assets/css/stylesAnalytics.css" />
  <link rel="stylesheet" href="./assets/css/loginStyles.css" />
  <link rel="stylesheet" href="./assets/css/searchBar.css" />
  <link rel="stylesheet" href="./assets/css/style.css" />
  <link rel="stylesheet" href="./assets/css/styles.css" />

  <link rel="icon" type="image/jpg" href="./assets/images/logocs.svg" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/angular-material@1.1.12/angular-material.min.css" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.1/css/boxicons.min.css" rel="stylesheet" />

  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/angular-material@1.1.23/angular-material.min.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/angular-material-icons@0.7.1/angular-material-icons.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css" />
</head>

<body ng-app="myApp">
  <div ng-controller="LoginController">

    <button ng-click="startNew()" class="startNew" ng-show="isLoggedIn && isAdmin">
      Start new
    </button>


    <div class="welcome-container">
      <img ng-show="!isAdmin && isLoggedIn" class="logo" src="./assets/images/logo.svg" alt="CrowdSnap">
    </div>
    <h3 class="welcome-message" ng-show="!isAdmin && isLoggedIn">Hi, {{userName || 'User'}}</h3>


    <!-- <button ng-click="logout()" class="logoutbutton" ng-show="!isAdmin && isLoggedIn">
        Logout
      </button> -->
      <div class="md-menu-container" ng-show="isLoggedIn && !isAdmin" style="display: flex; align-items: center; gap: 10px;">
        
        <md-icon 
        class="material-icons refresh-icon-button" 
        ng-click="refreshPage()"
        style="font-size: 30px !important">
        refresh
      </md-icon>
      
        <!-- Menu Button -->
        <md-menu>
          <md-button
            aria-label="Open Menu"
            class="md-icon-button menu-button"
            ng-click="$mdMenu.open($event)"
            md-menu-origin
            style="background: linear-gradient(135deg, #2C9E4B, #A0D405);  color: white; border-radius: 5px; padding: 10px 10px; font-size: 15px !important; display: flex !important;"
          >
            Menu
            <img src="./assets/images/drowDown.svg" class="dropdown-icon" style="width: 35px !important; height: auto !important;" />
          </md-button>
      
          <md-menu-content width="4">
            <md-menu-item ng-click="$mdMenu.close($event)" ng-show="$root.isConference">
              <md-icon class="material-icons">qr_code</md-icon>
              <a href="#!/qr">QR Scanner</a>
            </md-menu-item>
      
            <md-menu-item ng-click="$mdMenu.close($event)">
              <md-icon class="material-icons">casino</md-icon>
              <a href="#!/raffle">Go to Raffle Draw</a>
            </md-menu-item>
      
            <md-menu-item ng-click="$mdMenu.close($event)">
              <md-icon class="material-icons" style="color: #f35588">exit_to_app</md-icon>
              <md-button ng-click="logout()" style="color: #f35588">Logout</md-button>
            </md-menu-item>
          </md-menu-content>
        </md-menu>
      </div>
      
      


    <div class="md-menu-container" ng-show="isLoggedIn && isAdmin">
      <md-menu>
        <md-button aria-label="Open Menu" class="md-icon-button menu-button" ng-click="$mdMenu.open($event)"
          md-menu-origin
          style="background: linear-gradient(135deg, #2C9E4B, #A0D405);  color: white; border-radius: 5px; padding: 10px 10px; font-size: 15px !important; display: flex !important;">
          Menu
          <!-- <img src="./assets/images/drowDown.svg" class="dropdown-icon" /> -->
        </md-button>

        <md-menu-content width="4">
          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons">settings</md-icon>
            <md-button ng-click="openSettings()">Settings</md-button>
          </md-menu-item>

          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons">bar_chart</md-icon>
            <a href="#!/analytics">More Options</a>
          </md-menu-item>

          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons">qr_code</md-icon>
            <a href="#!/qr">QR Scanner</a>
          </md-menu-item>

          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons">upload</md-icon>
            <a href="#!/home">Go to Data Upload</a>
          </md-menu-item>

          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons">casino</md-icon>
            <a href="#!/raffle">Go to Raffle Draw</a>
          </md-menu-item>

          <md-menu-item ng-click="$mdMenu.close($event)">
            <md-icon class="material-icons" style="color: #f35588">exit_to_app</md-icon>
            <md-button ng-click="logout()" style="color: #f35588">Logout</md-button>
          </md-menu-item>
        </md-menu-content>
      </md-menu>
    </div>

    <div ng-view class="view"></div>
  </div>
  <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular-route.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/angular-animate@1.7.9/angular-animate.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/angular-aria@1.7.9/angular-aria.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/angular-messages@1.7.9/angular-messages.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/angular-material@1.1.23/angular-material.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/angular-material-icons@0.7.1/angular-material-icons.min.js"></script>
  <!-- <scriptsrc src="https://cdn.jsdelivr.net/npm/ngx-scanner-qrcode-14@1.0.17/bundles/ngx-scanner-qrcode.umd.min.js"></scriptsrc=>
  <script src="https://cdn.jsdelivr.net/npm/jsqr/dist/jsQR.js"></script> -->
  <!-- <script src="https://unpkg.com/jsqr/dist/jsQR.js"></script> -->

  <script src="app.js"></script>
  <script src="jsQR.js"></script>
  <script src="./services/valueMappingService.js"></script>
  <script src="./services/alertService.js"></script>
  <script src="./services/qrService.js"></script>
  <script src="./controllers/loginController.js"></script>
  <script src="./controllers/analyticsController.js"></script>
  <script src="./controllers/ValueMappingController.js"></script>
  <script src="./controllers/userrolecontroller.js"></script>
  <script src="./controllers/otpController.js"></script>
  <script src="./controllers/qrController.js?1"></script>
  <script src="./controllers/eventIDController.js"></script>
</body>

</html>