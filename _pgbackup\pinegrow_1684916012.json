{"files": {"index.html": {"frameworks": ["pg.insight.events", "pg.svg.lib", "angular", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "pg.html", "pg.components"]}}, "breakpoints": [], "frameworks": ["pg.insight.events", "pg.svg.lib", "angular", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "pg.html", "pg.components"], "urls": {"index.html": {"open-page-views": [{"w": 128, "h": 1000}]}}}