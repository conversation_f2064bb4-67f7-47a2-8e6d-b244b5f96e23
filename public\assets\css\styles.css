
#file-input {
  margin-top: 10px;
}

#email-textarea {
  margin-top: 10px;
  width: 100%;
  height: 200px;
}

.startNew {
  background: linear-gradient(135deg, #2C9E4B, #A0D405);
  color: #fff;
  font-family: 'Poppins', sans-serif;
  border-radius: 5px;
  border: 2px solid #2C9E4B;
  padding: 8px 16px;
  position: fixed;
  top: 30px; 
  right: 120px;
}
.logoutbutton {
  background: #fff;
  color: #F35588 ;
  font-family: 'Poppins', sans-serif;
  border-radius: 5px;
  border: 2px solid #F35588 ;
  padding: 8px 16px;
  position: fixed;
  top: 30px; 
  right: 80px;
}
.logoutbutton:hover{
  background-color: #fff;
}

.md-menu-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 10;
}
@media (max-width: 768px) {
  .md-menu-container {
    flex-direction: column-reverse;
    top: 80px;
  }
}



.menu-button {
  background-color: transparent;
  border: none;
}

.dropdown-icon {
  width: 24px; 
  height: 24px;
}

.md-menu-content {
  background-color: white;
  border-radius: 5px;
  padding: 10px;
  margin: 20px;
  z-index: 1015;
}

.welcome-message {
  color: #ffffff !important;
  margin-bottom: 40px;
  display: flex;
  justify-content: center !important;
  width: 100%;
}

#csvFile {
  display: none;
}


.welcome-container {
  display: flex; 
  width: 100%;
  justify-content: center;
  align-items: center; 
  height: auto; 
  text-align: center; 
}

.logo {
  display: flex;
  width: 391px;
  height: auto;
  margin-bottom: 20px;
  flex-direction: column;
  justify-content: center;
}

.logo:hover {
  transform: scale(1.05); 
}

@media (max-width: 600px) {
  .logo {
      width: 120px; 
      margin-right: 5px;
  }
}




#container {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  margin: 40 auto;
  padding: 40px 22px;
  border-radius: 18px;
  border: 1px dashed #FFF;
  border-radius: 10px;
  background-color: transparent !important;
  flex-direction: column;
  height: auto;
  width: 700px;
}
#dropbox{
  background-color: transparent;
  color: #fff;
}
#dropbox #jsonData{
  font-size: 10px;
}
#dropbox:hover {
  box-shadow: 0 0 20px 0px rgba(0, 0, 0, 0.7);
  transition: 0.75s;
}

#dropbox.dragover {
  background-color: #f0f0f0;
  width: 80vw; 
  height: 60vh;
  justify-content: center;
}

#dropbox:hover {
  background-color: #97f0af;
}



.file-label {
  width: 50%;
  height: 25%;
  padding: 10px 10px;
  font-size: 30px;
  font-family: Arial, Helvetica, sans-serif;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  margin: 20px auto;
}


.file-input {
  display: none;
  width: 50%;
}

.file-label:hover {
  box-shadow: 0 0 20px 0px rgba(0, 0, 0, 0.7);
  transition: 0.75s;
}

.file-label:active {
  background-color: #dcdcdc;
}

#container #jsonData {
  width: 50%;
  height: 90%;
  margin-top: 40px;
  font-size: 20px;
  font-family: Arial, Helvetica, sans-serif;
  border: 1px solid #0a021a;
  border-radius: 10px;
  padding: 30px;
  border-color: black;
  background-color: #f7f7f7;
  white-space: pre-wrap;
  word-wrap: break-word;

}
.loader {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .loader img {
      width: 55px !important;
      padding-right: 0px !important;
    }
    .loader h1 {
      font-size: 24px;
      padding: 0%;
      color: #333;
    }

.upload-status {
  margin-top: 10px;
  font-weight: bold;
  text-align: center;
}

.error-message {
  color: red;
  margin-top: 10px;
  text-align: center;
}

#idInput {
  display: flex; 
  flex-direction: column; 
  align-items: center; 
  width: 60%; 
  margin: 0 auto; 
  font-size: 15px;
}



input[type="number"] {
    -moz-appearance: textfield; 
}

input {
  border-radius: 8px; 
  border: 2px solid #FFF;
  background-color: transparent; 
  color: #fff; 
  margin-top: 10px; 
  padding: 10px; 
  font-size: 18px;
  width: 100%;
  box-sizing: border-box; 
  transition: border-color 0.3s ease, box-shadow 0.3s ease; 
}

input:focus {
  outline: none; 
  border-color: rgba(44, 158, 75, 1) !important;
  color: #fff !important;
  box-shadow: 0 0 10px rgba(44, 158, 75, 0.5); 
}

.inputBox:focus {
  color: #313A42  !important;
}

input:not(:placeholder-shown) {
  border-color: rgba(44, 158, 75, 1); 
  background-color: rgba(44, 158, 75, 0.1); 
}



.md-button:hover {
  background-color: #0056b3; 
}

h1, h3{
  color: #fff !important;
  text-align: center !important;
}

.count-display {
  font-family: Arial, sans-serif; 
  font-size: 24px; 
  color: white; 
  margin: 0;
}

.count-value {
  font-weight: bold;
  font-size: 28px;
  background: linear-gradient(135deg, #2C9E4B, #A0D405); 
  -webkit-background-clip: text; 
  -webkit-text-fill-color: transparent; 
  display: inline-block;
}

.numberlist {
  width: 70%;
  font-weight: bold;
  text-align: center !important;
  background: linear-gradient(135deg, #2C9E4B, #A0D405); 
  -webkit-background-clip: text; 
  -webkit-text-fill-color: transparent; 
  display: flex !important;
}


.notFound-value {
  font-weight: bold;
  font-size: 28px;
  background: linear-gradient(135deg, #fc4300, #ff0000); 
  -webkit-background-clip: text; 
  -webkit-text-fill-color: transparent; 
  display: inline-block;
}


table {
  width: 70%; 
  border-collapse: collapse; 
  margin: 20px 0; 
}

th, td {
  padding: 8px !important;
  text-align: center; 
  border: 1px solid #ddd; 
}

th {
  background-color: #f2f2f2; 
  font-weight: bold; 
}

tr:nth-child(even) {
  background-color: #f9f9f9; 
}
tr:nth-child(odd) {
  background-color: #dddddd; 
}

tr:hover {
  background-color: #f1f1f1; 
}


button:hover {
  background-color: #248c3e;
}

button:active {
  background-color: #1e7434;
}

md-dialog {
  border: 2px dashed #FFF;
  background-color: rgba(252, 251, 251, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 30px;
  padding-top: 40px;
  padding-bottom: 40px;
  width: 600px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.3);
}

md-dialog h1 {
  text-align: center;
  font-size: 28px;
  font-weight: 450 !important;
  color: #ffffff;
  margin-bottom: 30px;
}

md-checkbox .md-label{
  display: contents !important;
}
label {
  font-size: 16px;
  color: #ffffff;
  display: inline-block;
  width: 60%;
  padding: 0px;
}

.value-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.checkbox-group {
  display: flex;
  gap: 8px;
}

.value-checkbox {
  background-color: #333a40;
  color: #ffffff;
  border-radius: 5px;
  width: 35px;
  height: 35px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 16px;
  border: 2px solid #fff;
}

.value-checkbox:hover {
  background-color: #565e64;
}
md-checkbox .md-container{
  display: none !important;
}


.value-checkbox[aria-checked="true"],
.value-checkbox.md-checked {
  background-color: #333a40; 
  color: #37d67a; 
  border: 2px solid #37d67a; 
  box-shadow: 0px 0px 8px rgba(55, 214, 122, 0.6); 
}

md-dialog md-dialog-actions{
  justify-content: center !important;
}

.save-button {
  background-color: #ffffff !important;
  color: #0a021a;
  border: 1px solid #0a021a !important;
  border-radius: 10px;
  font-weight: bold;
  margin-top: 20px;
  padding: 10px;
  text-align: center;
  width: 50%;
  transition: background 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.save-button:hover {
  background: linear-gradient(135deg, #2C9E4B, #A0D405); 
  color: #ffffff;
  border-color: #005f1a !important;
  box-shadow: 0px 0px 50px rgba(221, 251, 229, 0.4); 
}

.save-button:disabled {
  background-color: #ffc5c5 !important;
  color: #000;
  cursor: not-allowed;
}

.controllerBox{
  display: block !important;
  border: 1px dashed #bcbcbc; 
  padding: 30px; 
  max-width: 400px; 
  margin: auto; 
  background-color: #f5f5f5; 
  border-radius: 8px;
  margin-top: 10px;
}
.custom-loader md-progress-circular {
  color: #2C9E4B !important;
}
.md-progress-circular circle {
  stroke: #2C9E4B !important; 
}

md-icon.material-icons {
  font-size: 24px; 
  color: #2C9E4B; 
  margin-right: 10px !important;
  width: 0px;
  vertical-align: middle; 
}


md-menu-item:hover {
  background-color: #f1f1f1; 
  cursor: pointer; 
}


.loading
{
    width: fit-content;
    margin: 30% auto 0;
    overflow: hidden;
}
.loading span{
    display: inline-block;
    padding: 2px;
    transform: translateY(70px);
    animation: loading 2s linear infinite;
}
@keyframes loading {
    0%{
        transform: translateY(70px);
    }
    20%{
        transform: translateY(0px);
    }
    100%{
        transform: translateY(0px);
    }
} 
.loading span:nth-child(2){
    animation-delay: 0.1s;
}  
.loading span:nth-child(3){
    animation-delay: 0.2s;
}  
.loading span:nth-child(4){
    animation-delay: 0.3s;
}  
.loading span:nth-child(5){
    animation-delay: 0.4s;
}  
.loading span:nth-child(6){
    animation-delay: 0.5s;
}  
.loading span:nth-child(7){
    animation-delay: 0.6s;
}  
.loading span:nth-child(8){
    animation-delay: 0.7s;
}  
.loading span:nth-child(9){
    animation-delay: 0.8s;
}  
.loading span:nth-child(10){
    animation-delay: 0.9s;
}  
.loading span:nth-child(11){
    animation-delay: 1.0s;
}  
.loading span:nth-child(12){
    animation-delay: 1.1s;
}  
.loading span:nth-child(13){
    animation-delay: 1.2s;
}  

/* QR scanner styles */


.analytics-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.toggle-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  background-color: #e8eef9;
  width: 100%;
  max-width: 400px;
  margin: 10px auto 30px;
}

.toggle-container button {
  flex: 1;
  padding: 15px 0;
  border: none;
  background: transparent;
  color: #6e7c91;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-container button.active {
  background-color: #A0D405;
  color: white;
  border-radius: 15px;
}

#video {
  display: block;
  margin: 0 auto;
  border-radius: 10px;
  background-color: #f0f3f8;
  border: 2px dashed #b8c4d9 !important;
  max-width: 80%;
  min-height: 225px;
  margin-bottom: 20px;
}

.scanner-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-select-wrapper {
  display: flex;
  justify-content: flex-end;
  margin: 20px 0;
  width: 100%;
  margin-left: -20px;
}

div[ng-show="selectedMode === 'Activity'"] {
  display: flex;
  align-items: center;
  padding: 0;
}

label[for="activitySelect"] {
  margin-right: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #313A42;
  white-space: nowrap;
}

#activitySelect {
  width: 150px;
  padding: 8px 30px 8px 12px;
  border-radius: 6px;
  border: 1px solid #d0d9e8;
  background-color: white;
  color: #313A42;
  font-size: 14px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236e7c91' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

#activitySelect:focus {
  outline: none;
  border-color: #a4ce39;
  box-shadow: 0 0 0 2px rgba(164, 206, 57, 0.2);
}

div[ng-if="scannedData"] {
  margin: 25px auto;
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.08);
  max-width: 450px;
}

div[ng-if="scannedData"] h3 {
  color: #313A42 !important;
  font-size: 18px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f3f8;
}

div[ng-if="scannedData"] h4 {
  color: #313A42;
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
  margin-bottom: 10px;
}

div[ng-if="scannedData"] p {
  color: #6e7c91;
  font-size: 15px;
  line-height: 1.5;
  word-break: break-all;
  background-color: #f9fafc;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #7b68ee;
}

div[ng-if="scannedData"] img {
  border-radius: 8px;
  border: 1px solid #e8eef9;
  max-width: 100%;
  height: auto;
  display: block;
  margin-top: 10px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.notification-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  border-radius: 10px;
}

.notification {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}

.success .icon-circle {
  background-color: rgba(39, 174, 96, 0.1);
  color: #27AE60;
}

.error .icon-circle {
  background-color: rgba(231, 76, 60, 0.1);
  color: #E74C3C;
}

.checkmark, .cross {
  font-size: 40px;
  font-weight: bold;
}

.participant-details {
  text-align: center;
  padding: 10px 10px;
  font-family: Arial, sans-serif;
  border: 1px solid #e8eef9;
  background-color: rgb(236, 236, 236);
  border-radius: 12px;
  margin-bottom: 5px;
}

.participant-name {
  margin: 0;
  font-size: 33px;
  font-weight: 600;
  color: #313A42 !important; 
  margin-bottom: 2px;
}

.participant-info {
  margin: 0;
  font-size: 14px;
  color: #999;
  line-height: 1.4;
}
.participant-info:first-of-type {
  font-size: 12px;
  color: #313A42;
  font-weight: 500;
}

.participant-info:last-of-type {
  font-size: 15px;
  color: #313A42; 
  font-weight: 800;
}

.status-message{
  margin-bottom: 10px !important;
}

.notification h2 {
  margin: 0 0 10px;
  font-size: 28px;
}

.success h2 {
  color: #27AE60;
}

.error h2 {
  color: #E74C3C;
}

.notification p {
  color: #666;
}
.close-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFE8E6;
  border-radius: 50%;
  z-index: 10;
}

.close-icon svg {
  width: 20px;
  height: 20px;
  stroke: #E63946;
  stroke-width: 2.5;
}

.close-icon:hover {
  background-color: #FFDAD6;
}
.action-button {
  background: linear-gradient(90deg, #2C9E4B, #A0D405); 
  border: 1px solid #5a5a5a;
  padding: 8px 30px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
}

.action-button:hover {
  background-color: #e0e0e0;
}


/* dropdown styles */
:root {
  --page-bkg: #313A42;
  --dropdown-bkg: #313A42;
  --dropdown-main-item-bkg: #313A42;
  --accent-color: #2d68ff;

  --text-color: #fff;
  --font-size: 1rem;
  --field: 0.5rem;

  --item-opacity: 0.4;

  --time: 0.2s;
}

/* Define the gradient property */
@property --gradient-angle {
  syntax: "<angle>";
  initial-value: -225deg;
  inherits: false;
}

/* Dropdown wrapper */
.dropdown-wrapper {
  position: relative;
  min-width: 200px;
  padding: var(--field);
  border-radius: 0.6rem;
  background-color: var(--dropdown-bkg);
  margin-top: 10px;
  z-index: 1000;
}

.dropdown-wrapper::after {
  --animation-name: none;
  content: "";
  position: absolute;
  inset: -2px -2px 0 0;
  z-index: -1;
  opacity: 0;
  border-radius: inherit;
  background-image: conic-gradient(from var(--gradient-angle),
          transparent 50%,
          var(--accent-color),
          transparent 60%);
  animation-name: var(--animation-name);
  animation-duration: 0.5s;
  animation-timing-function: linear;
  animation-fill-mode: both;
  transition: opacity 0;
}

/* Dropdown button */
.dropdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: var(--field);
  border-radius: inherit;
  background-color: var(--dropdown-main-item-bkg);
  transition: margin var(--time);
  color: var(--text-color);
  z-index: 1001;
  position: relative;
}

.dropdown::after {
  content: "";
  width: 1ch;
  aspect-ratio: 1/1;
  border-right: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
  transform: rotate(45deg);
  transition: border-color var(--time), transform var(--time);
}

/* Dropdown list */
.dropdown-list {
  display: grid;
  z-index: 1002; 
  top: 0px !important;
  grid-auto-rows: 0fr;
  max-height: max-content;
  overflow: hidden;
  border-radius: inherit;
  transition-property: gap, grid;
  background-color: #313A42;
  transition-duration: var(--time);
  position: absolute !important; 
  transition-timing-function: cubic-bezier(0.52, 1.67, 0.59, 0.48);
}

/* Dropdown list items */
.dropdown-list__item {
  overflow: hidden;
  padding-inline: var(--field);
  border-radius: inherit;
  background-color: rgba(39, 43, 48, var(--item-opacity));
  opacity: 0;
  visibility: hidden;
  transition-property: background-color, padding, opacity;
  transition-duration: var(--time);
  color: var(--text-color);
  text-align: left;
  z-index: 1003;
}

.dropdown-list__item:hover,
.dropdown-list__item:focus {
  --item-opacity: 0.7;
  outline-offset: -1px;
}

/* Open state */
._open.dropdown-wrapper::after {
  --animation-name: shine;
  opacity: 1;
  transition: opacity 0.2s;
}

._open .dropdown {
  margin-bottom: var(--field);
}

._open .dropdown::after {
  border-color: var(--accent-color);
  transform: rotate(-135deg);
}

._open .dropdown-list {
  grid-auto-rows: 1fr;
  gap: var(--field);
}

._open .dropdown-list__item {
  padding-top: var(--field);
  padding-bottom: var(--field);
  opacity: 1;
  visibility: visible;
}

/* Animation */
@keyframes shine {
  to {
      --gradient-angle: 0deg;
  }
}

.refresh-icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px !important;
  height: 34px !important;
  border: 2px solid #f35588;
  border-radius: 10px;
  background-color: transparent;
  color: #f35588;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 24px;
}

.refresh-icon-button:hover {
  background-color: rgba(243, 85, 136, 0.1);
  transform: scale(1.1);
}

.refresh-icon-button:active {
  background-color: rgba(243, 85, 136, 0.2);
  transform: scale(0.95);
}