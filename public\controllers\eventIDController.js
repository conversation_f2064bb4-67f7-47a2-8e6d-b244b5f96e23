angular.module('myApp')
  .controller('EventIDController', function ($scope, $mdDialog, eventid, $window, qrService, $http) {
    $scope.eventid = '';  
    $scope.errorMessage = '';
    $scope.activities = [];
    $scope.selectedActivity = {}; 

    $scope.submitEventID = function () {
      $scope.loading = true; 
      if ($scope.eventid) {
        $window.localStorage.setItem('eventid', $scope.eventid);

        qrService.loadActivities($scope.eventid).then(function(response) {
          $window.localStorage.setItem("eventsActivities", JSON.stringify(response));
          // console.log("activity response ", response);
          $scope.activities = response; 
          // $scope.showDropdown = true;
      $scope.loading = false; 
          $mdDialog.hide({ 
            eventid: $scope.eventid, 
            eventsActivities: $scope.activities
          });
        }).catch(function(error) {
          // console.log("Event id error ", error);
          $scope.errorMessage = 'Event ID is incorrect.';
        $window.localStorage.removeItem('eventsActivities');
        $window.localStorage.removeItem('eventid');
        $scope.loading = false; 
          console.error("Error loading activities:", error);
        });

      } else {
        $scope.errorMessage = 'Event ID is required.';
      }
    };

    // $scope.submitEventID = async function () {  
    //   try {
    //     $scope.errorMessage = ""; 
    //     $scope.loading = true; 
    
    //     const response = await $http({
    //       method: "POST",
    //       url: "https://crowdsnap.ai/api/polls/isvalidevent",
    //       data: { eventcode: $scope.eventid },
    //     });
    
    //     if (response.data == true) {
    //       console.log("The event ID is valid.");
    //       $window.localStorage.setItem('eventid', $scope.eventid);
    
    //       qrService.loadActivities($scope.eventid).then(function(response) {
    //         $window.localStorage.setItem("eventsActivities", JSON.stringify(response));
    //         $scope.activities = response; 
    
    //         $mdDialog.hide({ 
    //           eventid: $scope.eventid, 
    //           eventsActivities: $scope.activities
    //         });
    
    //       }).catch(function(error) {
    //         console.error("Error loading activities:", error);
    //         $scope.errorMessage = "Failed to load activities. Please try again.";
    //       });
    
    //     } else {
    //       $scope.errorMessage = 'Invalid Event ID. Please check and try again.';
    //       console.log("The event ID is invalid.");
    //     }
    
    //   } catch (error) {
    //     console.error("Error occurred during event ID validation:", error);
    //     $scope.errorMessage = "An error occurred while validating the event ID. Please try again.";
    //   } finally {
    //     $scope.loading = false;
    //   }
    // };

    $scope.selectActivity = function() {
      $window.localStorage.setItem("selectedEvent", JSON.stringify($scope.selectedActivity));
      if ($scope.selectedActivity) {
        $mdDialog.hide({ 
          eventid: $scope.eventid, 
          eventsActivities: $scope.activities, 
          selectedEvent: $scope.selectedActivity 
        });
      } else {
        $scope.errorMessage = 'Please select an activity.';
      }
    };

$scope.dropdownOpen = false;

$scope.toggleDropdown = function() {
  $scope.dropdownOpen = !$scope.dropdownOpen;
};

$scope.selectActivityItem = function(activity) {
  $scope.selectedActivity = {
    id: activity.id, 
    name: activity.name, 
    mapping: activity.mapping
  };
  $scope.dropdownOpen = false;
};
  });
