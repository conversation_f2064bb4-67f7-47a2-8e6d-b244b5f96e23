angular.module("myApp").controller("OtpController", [
  "$scope",
  "$mdDialog",
  "$http",
  "userid",
  "$window",
  function ($scope, $mdDialog, $http, userid, $window) {
    $scope.otp = ["", "", "", ""];
    $scope.showOtpLoader = false;
    $scope.token = "";
    $scope.otpCode = null;

    $scope.getOtpAsString = function () {
      return $scope.otp.join("");
    };
    $scope.formatOtp = function () {
        $scope.otpCode = $scope.otpCode.replace(/\D/g, '').slice(0, 4);
    };
    
    $scope.moveToNext = function (event, index) {
      const maxIndex = 3;
      const key = event.key;
      const inputs = document.querySelectorAll('input[type="number"]');

      if (/^[0-9]$/.test(key)) {
        $scope.otp[index] = key;

        if (index < maxIndex) {
          inputs[index + 1].focus();
        }
      } else if (key === "Backspace") {
        $scope.otp[index] = "";

        if (index > 0) {
          inputs[index - 1].focus();
        }
      }
    };

    $scope.verifyOtp = function () {
      $scope.showOtpLoader = true;
      // const otp = $scope.getOtpAsString();
      const otp = $scope.otpCode;

      if (!/^\d{4}$/.test(otp)) {
        $scope.showOtpLoader = false;
        $scope.errorMessage = "Please enter a valid 4-digit OTP.";
        return;
      }

      console.log("OTP code is", otp);

      const otpData = {
        otp: otp,
        userid: userid,
      };

      $http
        .post("https://www.crowdsnap.ai/api/client/otp", otpData)
        .then(function (response) {
          console.log("otp response", response);
          if (response.data !== false) {
            $scope.showOtpLoader = false;
            $mdDialog.hide("success");
            $window.localStorage.setItem("token", response.data.token);
            $window.localStorage.setItem("username", response.data.name);
            $window.localStorage.setItem("userdid", response.data.did);

            $scope.token = response.data.token;
          } else {
            $scope.showOtpLoader = false;
            $scope.errorMessage = response.data.message || "Invalid OTP";
          }
        })
        .catch(function (error) {
          $scope.showOtpLoader = false;
          console.error("Error verifying OTP:", error);
          $scope.errorMessage = "Error verifying OTP.";
        });
    };

    $scope.cancel = function () {
      $mdDialog.cancel();
    };
  },
]);
