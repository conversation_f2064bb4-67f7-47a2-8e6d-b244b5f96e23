<div ng-controller="ValueMappingController">
    <md-dialog aria-label="Value Mapping">
        <md-dialog-content>
            <h1>Value Mapping</h1>
            <div ng-repeat="value in uniqueStarValues" class="value-row">
                <label>{{ value }}</label>
                <div class="checkbox-group">
                    <md-checkbox 
                        ng-repeat="num in [1, 2, 3, 4, 5]" 
                        ng-checked="valueMapping[value] === num.toString()" 
                        ng-click="valueMapping[value] = num.toString()"
                        aria-label="Select {{num}}" 
                        class="value-checkbox"
                        ng-value="num.toString()">
                        {{ num }}
                    </md-checkbox>
                </div>
            </div>
        </md-dialog-content>
        <md-dialog-actions>
            <md-button class="save-button" ng-click="saveMapping()" ng-disabled="!isAllFilled()">Save</md-button>
        </md-dialog-actions>
    </md-dialog>
</div>
