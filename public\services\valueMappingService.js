angular.module('myApp')
  .service('valueMappingService', function() {
    var uniqueStarValues = [];

    this.captureStarValues = function(pollId, starValues) {
      starValues.forEach(function(value) {
        if (value && value.trim() !== '' && !uniqueStarValues.includes(value.trim())) {
          uniqueStarValues.push(value.trim());
        }
      });
    };

    this.getMappedValues = function() {
      return uniqueStarValues;
    };

    
  });
