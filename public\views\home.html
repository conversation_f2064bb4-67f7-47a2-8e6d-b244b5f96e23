<div ng-controller="LoginController" >
   
    
    <div class="welcome-container">
        <img ng-show="isAdmin && isLoggedIn" class="logo" src="./assets/images/logo.svg" alt="CrowdSnap">
    </div>
    <h3 class="welcome-message" ng-show="isAdmin && isLoggedIn">Hi, {{userName || 'User'}}</h3>
    

    <!-- Login Box -->
    <div class="loginContainer" ng-hide="isLoggedIn">
        <div class="wrapper">
          <div class="login-box">
            <div class="login-header">
              <span>Login</span>
            </div>
            <div class="input_box">
              <input type="text" id="user" class="input-field" ng-model="loginData.username" required>
              <label for="user" class="label">Username</label>
              <i class="bx bx-user icon"></i>
            </div>
            <div class="input_box">
              <input type="password" id="pass" class="input-field" ng-model="loginData.password" required ng-type="password">
              <label for="pass" class="label">Password</label>
              <i class="bx icon" ng-class="{'bx-hide': !isPasswordVisible, 'bx-show': isPasswordVisible}" ng-click="togglePassword()"></i>
            </div>
            <div class="input_box">
              <input type="submit" class="input-submit" value="Login" ng-click="login()">
            </div>
            <img ng-if="showLoginLoader" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px;">
            <div class="error-message" ng-show="error">{{ errorMessage }}</div>
          </div>
        </div>
      </div>
      
    
    <div id="container" style="display: flex;" ng-show="isLoggedIn">
    
    
    <!-- CSV file upload section -->
    <div ng-show="isLoggedIn" layout="column"style="display: contents;">
       
<div id="container" ng-controller="MainController" style="display: contents;" >    
    <div id="dropbox" ng-class="{'dragover': isDragOver}" ng-click="clickDropbox()" ng-drop="onFileDrop($event)" ng-show= "showdropbox">
        <input 
type="file" 
id="fileInput" 
accept=".csv" 
style="display: none;" 
onchange="angular.element(this).scope().onFileSelect(this.files[0])" />
        <img ng-hide="file" src="./assets/images/Upload icon.png" alt="data upload">
        <div ng-hide="file">
            <span>Click here or drag and drop a CSV file</span>
        </div>
        <div ng-if="file">File selected: {{file.name}}</div>
    </div>
    <!-- <md-button ng-show= "showdropbox" ng-click="logout()" class="btn-body sec-button_red md-button md-button md-button ng-scope md-ink-ripple"  style="color:#777; cursor: pointer;">Logout</md-button> -->

    <div ng-show="valuebox" style="display: contents;" layout="column">
        <h3 ng-show="hasStarType">Value Mappings for star rating Questions</h3>
        <table ng-show="hasStarType">
            <thead>
                <tr>
                    <th>NPS Text</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="(text, value) in valueMapping">
                    <td>{{ text }}</td>
                    <td>{{ value }}</td>
                </tr>
            </tbody>
        </table>
        <div>
            <md-button ng-if = "!showLoader && !showFinal && hasStarType" class="btn-body sec-button md-button ng-scope md-ink-ripple" ng-click="openValueMapping()" style="background-color: transparent !important;">Change Values</md-button>
            <md-button ng-if = "!showLoader && !showFinal"  class="md-raised md-accent md-button" ng-click="processData()">Start Processing</md-button>
    </div>
    </div>

    <div class = "loader"  style="display: contents;">
        <img ng-if = "showLoader" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px; padding-right: 10px;">
        <div ng-if="showLoader">
            <h1 class="count-display">Vote Count: <span class="count-value"># {{apiCallCount | number: 0}}</span></h1>
            <h1 class="count-display">Respondent Count: <span class="count-value"># {{responses | number: 0}}</span></h1>
        </div>                
        <h1 ng-if = "showFinal" class="count-display" >Successfully uploaded <span class="count-value">{{responses}}</span> respondents.</h1>
        <md-button ng-if = "showLoader" class="md-raised md-accent md-button" ng-click="stopProcess()">Stop Processing</md-button>
        <!-- <md-button ng-if = "showLoader" ng-click="logout()" class="btn-body sec-button_red md-button md-button md-button ng-scope md-ink-ripple"  style="color:#777; cursor: pointer;">Logout</md-button> -->
    </div>


    <div id="idInput" ng-show="showIndex" style="position: relative;">
        <input type="number" ng-model="idInput" placeholder="Please Enter Survey ID" 
               style="padding-left: 40px; background: url('./assets/images/surveyid.png') no-repeat 10px center; background-size: 20px 20px;">
        <img ng-if="showLoaderID" src="https://www.crowdsnap.ai/assets/images/loading_h.gif" style="width: 35px; padding-right: 10px;">
        <md-button ng-click="surveyIdInput()" class="btn-body sec-button md-button ng-scope md-ink-ripple">Submit</md-button>
    </div>
    
    
</div>

</div>