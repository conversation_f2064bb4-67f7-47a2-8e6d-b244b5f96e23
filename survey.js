const mysql2 = require('mysql2/promise');

class UserRole {
  constructor(dbConfig) {
    this.pool = mysql2.createPool(dbConfig);
  }

  async query(sql, values) {
    try {
      const [results, fields] = await this.pool.query(sql, values);
      return results;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  async assignMember(userRollId, userid, userName, userEmail, userNote) {
    const querySelect = "SELECT user_roll_id FROM users WHERE user_roll_id = ?";
    const updateQuery = "UPDATE users SET userid = ?, user_name = ?, user_email = ?, user_note = ? WHERE user_roll_id = ?";
    const insertQuery = "INSERT INTO users (user_roll_id, userid, user_name, user_email, user_note) VALUES (?, ?, ?, ?, ?)";
  
    try {
        const [existingUser] = await this.query(querySelect, [userRollId]);
      if (existingUser) {
        await this.query(updateQuery, [userid, userName, userEmail, userNote, userRollId]);
      } else {
        await this.query(insertQuery, [userRollId, userid, userName, userEmail, userNote]);
      }
      return 'Data inserted or updated into users table';
    } catch (error) {
      throw error;
    }
  }
  

  async getSurveyList() {
    const query = `SELECT ur.user_roll, ur.note, ur.user_roll_id, u.user_name FROM user_rolls ur LEFT JOIN users u ON ur.user_roll_id = u.user_roll_id`;
    try {
      const results = await this.query(query);
      return results;
    } catch (error) {
      throw error;
    }
  }


}


module.exports = UserRole;
