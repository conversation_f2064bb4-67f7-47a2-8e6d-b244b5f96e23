<div ng-controller="AnalyticsController" layout="column" class="analytics-container" layout-fill role="main" ng-show="isLoggedIn && isAdmin" >
        
  <md-tabs md-stretch-tabs class="md-primary" md-selected="data.selectedIndex">
    <md-tab id="tab1" aria-controls="tab1-content">
      Live Surveys
    </md-tab>
    <md-tab id="tab2" aria-controls="tab2-content">
      Add Credits
    </md-tab>
    <md-tab id="tab3" aria-controls="tab3-content">
      Add User
    </md-tab>
  </md-tabs>

             <!-- search bar -->
             <!-- <div class="search-bar">
              <div class="dropdown">
                <div id="drop-text" class="dropdown-text" ng-click="toggleDropdown()">
                  <span id="span">{{ selectedText }}</span>
                  <i id="icon" class="fa-solid fa-chevron-down" ng-style="{'transform': iconRotation}"></i>
                </div>
                <ul id="list" class="dropdown-list" ng-class="{'show': dropdownVisible}">
                  <li class="dropdown-list-item" ng-repeat="item in items" ng-click="selectItem(item)">
                    {{ item }}
                  </li>
                </ul>
              </div> -->
            
              <!-- Search Box -->
              <!-- <div class="search-box">
                <input type="text" id="search-input" placeholder="{{ searchPlaceholder }}" ng-model="searchQuery">
                <i class="fa-solid fa-magnifying-glass"></i>
              </div>
          </div> -->
          <!-- search bar end -->
           
<md-content flex md-scroll-y ng-controller="UserRoleController">
  <ui-view layout="column" layout-fill layout-padding>
    <div class="inset" hide-sm></div>
      <ng-switch on="data.selectedIndex" class="tabpanel-container">
        <div role="tabpanel"
             id="tab1-content"
             aria-labelledby="tab1"
             ng-switch-when="0"
             md-swipe-left="next()"
             md-swipe-right="previous()"
             layout="column" >
             
             <div ng-show="isLoading" layout="column" layout-align="center center" class="custom-loader" style="margin: 20px; color: #313A42;">
              <md-progress-circular md-mode="indeterminate"></md-progress-circular>
              <p>Loading Surveys, please wait...</p>
          </div>
          
                  <md-item ng-repeat="item in activity | filter:search" >
                      <div layout="row" layout-align="space-between center" style="width: 100%; display: flex; flex-wrap: wrap;" class="controllerBox">
                          
                          <div class="md-tile-content" style="flex: 1;">
                              <h3>{{item.user_roll}}</h3>
                              <h6>{{item.note}}</h6>
                              <div class="md-tile-left inset" hide-sm>
                                  <img src="assets/images/ur.svg">
                              </div>
                          </div>
                  
                          <div layout="column" layout-align="end center" style="flex-shrink: 0;">
                              <div ng-controller="AnalyticsController as ctrl" ng-cloak>
                                  <h6>{{item.user_name}}</h6>
                                  <md-button ng-click="showAddUser($event, item.user_roll_id, item.user_roll)" class="btn-body sec-button md-button ng-scope md-ink-ripple">
                                      <md-icon md-svg-src="assets/images/person_add.svg" class="ng-scope" role="img" aria-hidden="true"></md-icon> Assign A Administrator
                                  </md-button>
                              </div>
                          </div>
                      </div>
                  
                  
                    <md-divider md-inset hide-sm ng-if="!$last" style="margin-bottom: 20px; padding-left: 10px !important;"></md-divider>

                    <md-divider hide-gt-sm ng-if="!$last"></md-divider>
                  </md-item>
        </div>
        <div role="tabpanel" ng-switch-when="1">
          <div class="controllerBox" layout="column" layout-align="center center" >
            <md-input-container style="width: 100%; margin-bottom: 20px;">
                <md-icon md-svg-src="assets/images/user.svg"></md-icon>
                <input class="inputBox" type="number" placeholder="User ID" ng-model="userId">
            </md-input-container>
        
            <md-input-container style="width: 100%; margin-bottom: 20px;">
                <md-icon md-svg-src="assets/images/hash.svg"></md-icon>
                <input class="inputBox" type="number" placeholder="Number of Credits" ng-model="credits">
            </md-input-container>
        
            <md-button ng-click="addCredits(userId, credits)" class="md-raised md-primary" style="width: 100%; max-width: 200px; background-color: #333; color: white !important; ">
                ADD CREDITS
            </md-button>
        </div>
      </div>

<!-- <div role="tabpanel" ng-switch-when="2">
        <div class="controllerBox" layout="column" layout-align="center center">
            <md-input-container style="width: 100%; margin-bottom: 20px;">
                <md-icon md-svg-src="assets/images/user.svg"></md-icon>
                <input class="inputBox" type="email" placeholder="User email" ng-model="username">
            </md-input-container>
    
            <md-input-container style="width: 100%; margin-bottom: 20px;">
                <md-icon md-svg-src="assets/images/key.svg"></md-icon>
                <input class="inputBox" type="text" placeholder="Password" ng-model="password">
            </md-input-container>
    
            <md-button ng-click="addUser(username,password)" class="md-raised md-primary" style="width: 100%; max-width: 200px; background-color: #333; color: white !important;">
                ADD USER
            </md-button>
        </div>
    </div> -->

    <div role="tabpanel" ng-switch-when="2" >
      <div class="controllerBox" layout="column" layout-align="center center">
          <md-input-container style="width: 100%; margin-bottom: 20px;">
              <md-icon md-svg-src="assets/images/user.svg"></md-icon>
              <input class="inputBox" type="text" placeholder="Survey Id" ng-model="clientid">
          </md-input-container>
          
          <md-input-container style="width: 100%; margin-bottom: 20px;">
            <md-icon md-svg-src="assets/images/hash.svg"></md-icon>
            <input class="inputBox" type="text" placeholder="Poll ID" ng-model="pollid">
        </md-input-container>
  
        <md-input-container style="width: 100%; margin-bottom: 20px;">
          <md-icon md-svg-src="assets/images/time.svg"></md-icon>
          <label>Select Start Time</label>
          <input type="datetime-local" ng-model="startTime">
      </md-input-container>
      
      <md-input-container style="width: 100%; margin-bottom: 20px;">
        <md-icon md-svg-src="assets/images/time.svg"></md-icon>
          <label>Select End Time</label>
          <input type="datetime-local" ng-model="endTime">
      </md-input-container>
  
          <md-button ng-click="getRandomVote(clientid,pollid,startTime,endTime)" class="md-raised md-primary" style="width: 100%; max-width: 200px; background-color: #333; color: white !important;">
              Submit
          </md-button>
      </div>
  </div>
    
    </ng-switch>
    
  </ui-view>
</md-content>
</div>