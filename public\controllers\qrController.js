angular
  .module("myApp")
  .controller(
    "QRController",
    function (
      $scope,
      $mdDialog,
      qrService,
      $window,
      $rootScope,
      $http,
      $interval
    ) {
      $scope.isScannerOpen = true;
      $scope.selectedActivity = {};
      let selectedEventData = $window.localStorage.getItem("selectedEvent");
      if (!selectedEventData || selectedEventData === "undefined") {
        $scope.selectedEvent = {};
      } else {
        try {
          $scope.selectedEvent = JSON.parse(selectedEventData);
        } catch (e) {
          console.error("Error parsing selectedEvent from localStorage:", e);
          $scope.selectedEvent = {};
        }
      }

      // console.log("selected event is ", $scope.selectedEvent);
      $scope.selectedMode = $scope.selectedEvent.name || "event";
      $scope.scannedData = null;
      let decodedData = null;
      $scope.qrImage = null;
      let videoStream = null;
      let mappingValue = 0;
      $scope.eventid = $window.localStorage.getItem("eventid");
      $scope.activities = [];
      $scope.eventsActivities = JSON.parse(
        localStorage.getItem("eventsActivities") || "[]"
      );

      $scope.checkTokenAuthorization = function () {
        const token = $window.localStorage.getItem("token");
        if (!token) {
          console.error("Authorization token not found.");
          $rootScope.$broadcast("logoutEvent");
          return;
        }

        const payload = { eventid: $scope.eventid };
        const headers = { Authorization: token };
        if ($scope.eventid != null) {
          $http
            .post(
              "https://www.crowdsnap.ai/api/polls/shareinminipay",
              payload,
              {
                headers: headers,
              }
            )
            .then(function (response) {
              // console.log("Token authorization successful:", response.data);
              if (response.data == false) {
                $rootScope.$broadcast("logoutEvent");
                window.location.reload();
              }
            })
            .catch(function (error) {
              console.error("Token authorization failed:", error);
              if (error.status === 401 || error.status === 403) {
                alert("Session expired. Please log in again."); 
                $rootScope.$broadcast("logoutEvent");
                window.location.reload();
              }
            });
        }
      };
      $scope.checkTokenAuthorization();

      $interval($scope.checkTokenAuthorization, 300000);

      $scope.showEventIdpDialog = function () {
        // console.log("show otp is working");
        $mdDialog
          .show({
            controller: "EventIDController",
            templateUrl: "./views/eventIDBox.html",
            parent: angular.element(document.body),
            clickOutsideToClose: false,
            locals: { eventid: $scope.eventid },
          })
          .then(function (result) {
            $window.location.href = "#!/landing";
            const { eventid, eventsActivities } = result;
            // console.log("Event ID submitted:", eventid, eventsActivities);
            $scope.eventid = eventid;
            $scope.eventsActivities = eventsActivities;
            const loadActivity = $scope.eventsActivities.find(
              (activity) => activity.name === "event"
            );
        location.reload();
            $window.localStorage.setItem(
              "selectedEvent",
              JSON.stringify(loadActivity)
            );
            $scope.selectedEvent = loadActivity;
            $scope.loadActivities();
            $scope.startScanning();
          });
      };

      $scope.loadActivities = async function () {
        try {
          const isConference = $scope.eventsActivities.find(
            (activity) => activity.name === "cs_isconference"
          );
          const conferenceMapping = isConference.mapping;
      
          localStorage.setItem("isConference", conferenceMapping == 1 ? "true" : "false");
      
          $rootScope.isConference =  $window.localStorage.getItem("isConference");
          // console.log("is confereence ",  $rootScope.isConference);
      
          const loadActivity = $scope.eventsActivities.find(
            (activity) => activity.name === "activities"
          );
          mappingValue = loadActivity.mapping;
          const response = await qrService.loadActivities(mappingValue);
          // console.log("Activity response:", response);
      
          $scope.isScannerOpen = true;
          $scope.scannedData = null;
          $scope.qrImage = null;
          $scope.activities = response;
          // console.log("Selected event is ", $scope.selectedEvent.name);
        } catch (error) {
          console.error("Error loading activities:", error);
        }
      };
      

      $scope.toggleScanMode = async function (mode) {
        $scope.selectedMode = mode;
        if ($scope.selectedMode === "event") {
          const loadActivity = $scope.eventsActivities.find(
            (activity) => activity.name === "event"
          );
          $window.localStorage.setItem(
            "selectedEvent",
            JSON.stringify(loadActivity)
          );
          $scope.selectedEvent = loadActivity;
          // console.log("Activity response:", loadActivity);
        } else {
          const loadActivity = $scope.eventsActivities.find(
            (activity) => activity.name === "activities"
          );
          $window.localStorage.setItem(
            "selectedEvent",
            JSON.stringify(loadActivity)
          );
          $scope.selectedEvent = loadActivity;
          // console.log("Activity response:", loadActivity);
        }
      };

      $scope.startScanning = async function () {
        const video = document.getElementById("video");
        const canvas = document.getElementById("canvas");
        const ctx = canvas.getContext("2d", { willReadFrequently: true });
        ctx.willReadFrequently = true;
      
        let scanning = true;
        let videoStream;
      
        function stopCamera() {
            scanning = false;
            $scope.isScannerOpen = false;
            if (videoStream) {
                videoStream.getTracks().forEach(track => {
                    track.stop();
                    track.enabled = false;
                });
            }
        }
      
        try {
            let stream;
            try {
              stream = await navigator.mediaDevices.getUserMedia({
                video: {
                  facingMode: "environment",
                }
              });
              
            } catch (err) {
                console.log("Falling back to lenient constraints");
                stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: "environment", 
                    }
                });
            }
            
            videoStream = stream;
            video.srcObject = stream;
            
            const videoTrack = stream.getVideoTracks()[0];
            if (videoTrack && videoTrack.getCapabilities) {
                const capabilities = videoTrack.getCapabilities();
                const settings = videoTrack.getSettings();
                
                if (capabilities.focusMode && capabilities.focusMode.includes('continuous')) {
                    try {
                        await videoTrack.applyConstraints({
                            advanced: [{ focusMode: 'continuous' }]
                        });
                    } catch (e) {
                        console.log("Could not set continuous focus", e);
                    }
                }
                
                if (capabilities.zoom && settings.zoom !== 1) {
                    try {
                        await videoTrack.applyConstraints({
                            advanced: [{ zoom: 1 }]
                        });
                    } catch (e) {
                        console.log("Could not reset zoom", e);
                    }
                }
            }
            
            video.play().catch(err => {
                console.error("Error playing video:", err);
                stopCamera();
                $scope.$apply(() => {
                    $scope.scannedData = "Camera error. Please try again.";
                });
            });
        
            video.addEventListener("playing", function onPlaying() {
                video.removeEventListener("playing", onPlaying);
                
                const videoAspect = video.videoWidth / video.videoHeight;
                const canvasAspect = canvas.width / canvas.height;
                
                let renderWidth, renderHeight;
                if (videoAspect > canvasAspect) {
                    renderHeight = canvas.height;
                    renderWidth = video.videoWidth * (canvas.height / video.videoHeight);
                } else {
                    renderWidth = canvas.width;
                    renderHeight = video.videoHeight * (canvas.width / video.videoWidth);
                }
                
                const xOffset = (canvas.width - renderWidth) / 2;
                const yOffset = (canvas.height - renderHeight) / 2;
                
                function scan() {
                  if (!scanning) return;
                
                  try {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(video, xOffset, yOffset, renderWidth, renderHeight);
                    const scanSize = Math.min(canvas.width, canvas.height) ;
                    const x = (canvas.width - scanSize) / 2;
                    const y = (canvas.height - scanSize) / 2;
                    const imageData = ctx.getImageData(x, y, scanSize, scanSize);
                    const code = jsQR(imageData.data, scanSize, scanSize, {
                      inversionAttempts: "dontInvert"
                    });
                
                    if (code) {
                      handleQRCode(code);
                    } else {
                      setTimeout(() => requestAnimationFrame(scan), 100); 
                    }
                
                  } catch (err) {
                    console.error("Scan error:", err);
                    requestAnimationFrame(scan);
                  }
                }
                
                
                scan();
            });
            
            async function handleQRCode(code) {
              ctx.strokeStyle = 'red';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(code.location.topLeftCorner.x, code.location.topLeftCorner.y);
    ctx.lineTo(code.location.topRightCorner.x, code.location.topRightCorner.y);
    ctx.lineTo(code.location.bottomRightCorner.x, code.location.bottomRightCorner.y);
    ctx.lineTo(code.location.bottomLeftCorner.x, code.location.bottomLeftCorner.y);
    ctx.closePath();
    ctx.stroke();
    
    canvas.style.display = 'block';
    video.style.display = 'none';
    
    
    await new Promise(resolve => setTimeout(resolve, 500));
                scanning = false;
                try {
                    const decodedData = atob(code.data);
                    $scope.scannedData = decodedData;
                    $scope.qrImage = canvas.toDataURL();
                    const qrData = JSON.parse(decodedData);
                    stopCamera();
                    $scope.name = qrData.name;
                    $scope.company = qrData.company;
                    $scope.designation = qrData.designation;
                    const eventid = qrData.eventid;
                    const guid = qrData.guid;
                    try {
                      if ($scope.selectedEvent.name === "event") {
                        const response = await qrService.verifyParticipant(eventid, guid);
            
                        if (response === 1) {
                          $scope.isVerified = 1;
                          $scope.scannedData = "Participant Verified Successfully!";
                          const response2 = qrService.saveVote( mappingValue, guid, $scope.selectedActivity, 1);
                          console.log("Enterance vote submitted", response2);
                        } else if (response === 0) {
                          $scope.isVerified = 0;
                          $scope.scannedData = "QR Error. Please try again!";
                        } else if (response === -1) {
                          $scope.isVerified = -1;
                          $scope.scannedData = "Participant Already Verified!";
                        }
                        $scope.$apply();
                      } else if ($scope.selectedEvent.name === "activities") {
                        if (Object.keys($scope.selectedActivity).length === 0) {
                          $scope.$apply(function () {
                            $scope.isVerified = 404;
                            $scope.scannedData = "Please select an activity";
                          });
                        } else {
                          const response = await qrService.verifyParticipantActivity(eventid, guid);
                          // if verified there will be verification record
                          if (response.length === 0) {
                            $scope.isVerified = 0;
                            $scope.scannedData = "QR Error. Please try again!";
                          }else{
                            const response2 = await qrService.saveVote(
                              $scope.selectedEvent.mapping,
                              guid,
                              $scope.selectedActivity,
                              0
                            );
              
                            if (response2 === 1) {
                              $scope.isVerified = 1;
                              $scope.scannedData = "Your Attendance Has Been Recorded";
                            } else if (response2 === 0) {
                              $scope.isVerified = -1;
                              $scope.scannedData = "You're already recorded!";
                            } else {
                              $rootScope.$broadcast("logoutEvent");
                            }
              
                            // console.log("selected Activity", $scope.selectedActivity);
                          }
                          $scope.$apply();
                        }
                      }
                    } catch (error) {
                      $scope.$apply(function () {
                        $scope.scannedData = "Verification failed. Please try again.";
                        // console.log( error);
                      });
                    }
                    
                } catch (err) {
                    console.error("QR processing error:", err);
                    $scope.$apply(() => {
                        $scope.scannedData = "Error processing QR code";
                    });
                } finally {
                    stopCamera();
                    canvas.style.display = 'none';
                    video.style.display = 'block';
                    $scope.$apply();
                }
            }
            
        } catch (err) {
            console.error("Camera access error:", err);
            $scope.$apply(() => {
                $scope.scannedData = "Could not access camera. Please check permissions.";
            });
        }
    };


      $scope.activityDropdownOpen = false;

      $scope.toggleActivityDropdown = function () {
        $scope.activityDropdownOpen = !$scope.activityDropdownOpen;
      };

      $scope.selectActivityItem = function (activity) {
        $scope.selectedActivity = {
          id: activity.id,
          name: activity.name,
        };
        $scope.activityDropdownOpen = false;
      };

     

      $scope.done = function () {
        $scope.isScannerOpen = true;
        $scope.scannedData = null;
        $scope.qrImage = null;
        $scope.isVerified = null;

        $scope.startScanning();
      };
      if (!$window.localStorage.getItem("isLoggedIn")) {
        $window.location.href = "#!/login";
        window.location.reload();
      }
      if ($window.localStorage.getItem("eventid") === null) {
        $scope.showEventIdpDialog();
      } else {
        $scope.loadActivities();
        $scope.startScanning();
      }
    }
  );
