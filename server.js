const express = require('express');
const open = require('open');
const path = require('path');
const bodyParser = require("body-parser");
const Surveys = require("./survey");
const cors = require('cors');
require('dotenv').config();

const PORT = process.env.PORT || 8080;
const app = express();

app.use(express.static(path.join(__dirname, 'public')));

app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(cors());

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});


const dbConfig = {
host: process.env.HOST,
port: process.env.DBPORT,
user: process.env.USER,
password: process.env.PASSWORD,
database: process.env.DATABASE,
ssl: {
  rejectUnauthorized: false,
},
};
console.log("database data",dbConfig);

const surveys = new Surveys(dbConfig);

app.post("/login", async (req, res) => {
  const { username } = req.body;

  const query = 'SELECT * FROM adminUsers WHERE username = ?';
  
  try {
    const [results] = await surveys.query(query, [username]);
    console.log("reading success ", results)
    
    if (results.success === true) {
      res.json({ success: true, message: 'Login successful' });
    } else {
      res.json({ success: false, message: 'Invalid username or password' });
    }
  } catch (err) {
    console.error("Error connecting to database:", err);
    res.status(500).send("Error connecting to database");
  }
});


app.post("/assignmember", async (req, res) => {
const { survey_id, user_email, user_note } = req.body;
try {
  await surveys.assignMember( survey_id, null , null, user_email, user_note);
  res.send('Data inserted into users table');
} catch (error) {
  console.error("Error assigning member:", error);
  res.status(500).send(error.message);
}
});


app.post("/addCredits", async (req, res) => {
  const { userid, credits } = req.body;

  if (!userid || !credits) {
    return res.status(400).json({ success: false, message: "userid and credits are required" });
  }

  try {
    const creditsResponse = await axios.post("https://crowdsnap.ai/api/addcredits", {userid,credits});

    console.log("Response from add credits:", creditsResponse);
    res.json({ success: true, message: 'Credits added successfully' });
  } catch (error) {
    console.error("Error adding credits:", error);
    res.status(500).json({ success: false, message: "Error adding credits" });
  }
});


app.post("/addUser", async (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ success: false, message: "Username and password are required" });
  }

  try {
    const query = 'INSERT INTO adminUsers (username, password) VALUES (?, ?)';
    const result = await surveys.query(query, [username, password]);

    console.log("User added successfully:", result);
    res.json({ success: true, message: 'User added successfully' });
  } catch (error) {
    console.error("Error adding user to database:", error);
    res.status(500).json({ success: false, message: "Failed to add user" });
  }
});


app.get("/getsurveys", async (req, res) => {
try {
  const users = await surveys.getSurveyList();
  res.json(users);
} catch (error) {
  console.error('Error fetching users:', error);
  res.status(500).send(error.message);
}
});

app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
  });

//sample survey id = 3604837

// sample pollid 9803750
// sample survey id 7294836

// find the winner, winner list
// add name to list