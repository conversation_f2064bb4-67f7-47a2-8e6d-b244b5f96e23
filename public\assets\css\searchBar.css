:root{
    --green: #A0D405;
    --darkgreen: #2C9E4B;
    --white: #ffffff;
    --shadow: rgba(0,0,0,0.15) 0px 5px 15px 0px00;
}


.search-bar{
    display: flex;
    align-items: center;
    min-width: 80%;
    border-radius: 30px !important;
}

.dropdown {
    position: relative;
    width: 280px;
    border-radius: 50px;
    border: 1px solid var(--white);
    background-color: var(--darkgreen);
    box-shadow: var(--shadow);
    cursor: pointer;
}

.dropdown-text {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1rem;
    font-weight: 500;
    color: var(--white);
    padding: 1rem 1.5rem;
}

.dropdown-list {
    position: absolute;
    top: 4rem;
    left: 0;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    border-radius: 15px;
    background-color: var(--white);
    transition: max-height 0.5s;
}

#list.show {
    max-height: 300px;
}
.dropdown-list-item {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 1rem 0 1rem 1.5rem;
    cursor: pointer;
    transition: margin-left 0.2s ease-in-out , color 0.2s ease-in-out;
}

.dropdown-list-item:hover{
    margin-left: 0.5rem;
    color: var(--darkgreen);
}

.search-box {
    display: flex;
    align-items: center;
    padding-right: 1rem;
    width: 100%;
    color: var(--darkgreen);  
    border-top: none !important;
    box-shadow: none !important
  }


.search-box input {
    padding: 1rem;
    width: 100%;
    font-size: 1rem;
    font-weight: 500;
    color: var(--darkgreen);
    border: 0;
    outline: 0;
}

.search-box i {
    font-size: 1.3rem;
    cursor: pointer;
}

.search-box input::placeholder{
    font-size: 1rem;
    font-weight: 500;
    color: var(--darkgreen);
}