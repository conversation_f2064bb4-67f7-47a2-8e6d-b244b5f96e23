/* POPPINS FONT */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

/* ===== COLOR VARIABLES ===== */

:root{
    --primary-color: #2C9E4B;
    --second-color: #ffffff; 
    --black-color: #313A42;
}


/* ===== Reusable CSS ===== */

a{
    text-decoration: none;
    color: var(--second-color);
}
a:hover{
    text-decoration: underline;

}


/* ===== WRAPPER ===== */

.wrapper{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-box{
    position: relative;
    width: 450px;
    border: 3px solid var(--primary-color);
    border-radius: 15px;
    padding: 7.5em 2.5em 4em 2.5em;
    background-color: #ffffff;
    box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.3);
}

.login-header{
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    /* background-color:var(--primary-color); */
    width: 140px;
    height: 70px;
    border-radius: 0 0 20px 20px ;
}

.login-header span {
    padding-top: 35px;
    font-size: 40px;
    font-weight: 400 ;
    color: var(--black-color);
}

.login-header::before{
    content: "";
    position: absolute;
    top: 0;
    left: -30px; 
    width: 30px;
    height: 30px;
    border-top-right-radius: 50%;
    background: transparent;
    /* box-shadow: 15px 0 0 0 var(--primary-color); */
}

.login-header::after{
    content: "";
    position: absolute;
    top: 0;
    right: -30px; 
    width: 30px;
    height: 30px;
    border-top-left-radius: 50%;
    background: transparent;
    /* box-shadow: -15px 0 0 0 var(--primary-color); */
}

.input_box{
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 20px 0;
}

.input-field{
    width: 100%;
    height: 55px;
    font-size: 16px;
    background: transparent;
    color: #000000 !important;
    padding-inline: 20px 50px;
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    outline: none;
}

#user{
    margin-bottom: 10px;
}

.label{
    position: absolute;
    color: #313A42 !important;
    top: 23px;
    left: 20px;
    transition: .2s ease-in-out;
}

.input-field:focus ~ .label,
.input-field:valid ~ .label{
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 14px;
    background-color: var(--primary-color) !important;
    border-radius: 30px;
    color: #fff !important;
    padding: 0 10px;

}
.loginContainer .wrapper .login-box .input_box .input-field:focus {
    font-weight: 300 !important;
    color: #313A42 !important;
    box-shadow: 0 0 15px rgba(4, 95, 36, 0.5); 
    outline: none; 
}

.icon{
    position: absolute;
    top: 25px;
    right: 25px;
    font-size: 20px;
}

.remember-forgot{
    display: flex;
    justify-content: space-between;
    font-size: 15px;
}
.disabled-btn {
    opacity: 0.5;
    pointer-events: none;
  }
  
.input-submit{
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #2C9E4B, #A0D405);
    font-size: 20px;
    font-weight: 500;
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    cursor: pointer;
    transition: .5s ease-in-out;
}

.input-submit:hover{
    background: #A0D405;
}

.register{
    text-align: center;
}

.register a{
    font-weight: 500;
}

a{
    color: #A0D405;
}
.privacy-icon {
    width: 80px;
    height: auto;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    object-fit: cover;
}

@media only screen and (max-width: 564px) {
    .wrapper{
        padding: 20px;
    }
    .login-box{
        padding: 7.5em 1.5em 4em 2.5em;
    }
}