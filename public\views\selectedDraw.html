<md-dialog aria-label="Selection Dialog">
    <md-dialog-content ng-if="!isNull" style="text-align: center !important;">
        <h3 ng-if="isLoadingAnimation" style="color: #fff !important;margin-bottom: 30px;" class="loading">
                <span>P</span>
                <span>r</span>
                <span>o</span>
                <span>c</span>
                <span>e</span>
                <span>s</span>
                <span>s</span>
                <span>i</span>
                <span>n</span>
                <span>g</span>
                <span>.</span>
                <span>.</span>
                <span>.</span>
        </h3>
        <h3 ng-if="!isLoadingAnimation" style="color: #fff !important;margin-bottom: 30px; font-size: 40px;">Success</h3>
        <!-- Loader: Rolling number animation -->
        <h1 class="count-value" style="font-weight: 800 !important;">
            <span ng-if="isLoadingAnimation">07{{ animatedNumber }}</span>
            <span ng-if="!isLoadingAnimation" style="font-size: 40px !important;">0{{ selection.mobile_number }}</span>
            <span ng-if="!isLoadingAnimation" style="font-size: 30px !important;"> - {{ selection.participant_name }}</span>
        </h1>
    </md-dialog-content>
    <md-dialog-content ng-if="isNull" style="text-align: center;">
        <h2 style="color: #fff !important;margin-bottom: 30px;">Error</h2>
        <h1 class="notFound-value">Mobile number not found.</h1>
    </md-dialog-content>
    <md-dialog-actions>
        <md-button ng-click="closeDialog()" class="btn-body sec-button md-button ng-scope md-ink-ripple">
            OK
        </md-button>
    </md-dialog-actions>
</md-dialog>
